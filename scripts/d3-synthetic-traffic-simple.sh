#!/bin/bash
# D3 Phase: Simplified Synthetic Traffic Test for MCP Rules Engine
# Runs a simplified load test against the Vercel MCP service

set -e

# Configuration
STAGING_URL="https://pi-lawyer-mcp-rules-k46fd4vfq-jpkays-projects.vercel.app"
MCP_ENDPOINT="/api"
RATE=5
COUNT=100  # Reduced for faster testing
BATCH_SIZE=5

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 D3 Phase: Simplified MCP Rules Engine Synthetic Traffic Test${NC}"
echo "=================================================="
echo "Target: ${STAGING_URL}${MCP_ENDPOINT}"
echo "Rate: ${RATE} req/sec"
echo "Count: ${COUNT} requests"
echo ""

# Create test payload
echo -e "${YELLOW}📝 Creating test payload...${NC}"

TEST_PAYLOAD='{
  "jurisdiction": "TX_STATE",
  "triggerCode": "SERVICE_OF_PROCESS",
  "startDate": "2025-07-01",
  "practiceArea": "personal_injury"
}'

echo -e "${GREEN}✅ Test payload created${NC}"
echo ""

# Function to make a single request
make_request() {
    local request_id=$1
    local response=$(curl -s -w "%{http_code}" -X POST "${STAGING_URL}${MCP_ENDPOINT}" \
        -H "Content-Type: application/json" \
        -H "User-Agent: D3-SyntheticTraffic-Simple/1.0" \
        -d "${TEST_PAYLOAD}" \
        -o /tmp/response_${request_id}.json 2>/dev/null)
    
    local http_code="${response: -3}"
    echo "${http_code}"
}

# Initialize results
echo -e "${YELLOW}🔥 Starting synthetic traffic burst...${NC}"
echo "This will take approximately $((COUNT / RATE)) seconds..."
echo ""

SUCCESSFUL_REQUESTS=0
FAILED_REQUESTS=0
TOTAL_REQUESTS=0

# Run requests in batches
TOTAL_BATCHES=$((COUNT / BATCH_SIZE))

for ((batch=1; batch<=TOTAL_BATCHES; batch++)); do
    echo -e "${BLUE}Batch ${batch}/${TOTAL_BATCHES}...${NC}"
    
    # Run batch of requests
    for ((i=1; i<=BATCH_SIZE; i++)); do
        REQUEST_ID="${batch}_${i}"
        HTTP_CODE=$(make_request "$REQUEST_ID")
        
        TOTAL_REQUESTS=$((TOTAL_REQUESTS + 1))
        
        if [[ "$HTTP_CODE" == "200" ]]; then
            SUCCESSFUL_REQUESTS=$((SUCCESSFUL_REQUESTS + 1))
            echo "  ✅ Request ${REQUEST_ID}: HTTP ${HTTP_CODE}"
        else
            FAILED_REQUESTS=$((FAILED_REQUESTS + 1))
            echo "  ❌ Request ${REQUEST_ID}: HTTP ${HTTP_CODE}"
        fi
    done
    
    # Rate limiting - wait between batches
    if [ $batch -lt $TOTAL_BATCHES ]; then
        sleep $((BATCH_SIZE / RATE))
    fi
done

echo ""
echo -e "${GREEN}✅ Synthetic traffic test completed!${NC}"
echo ""

# Analyze results
echo -e "${YELLOW}📈 Analyzing results...${NC}"

# Calculate success rate
if [ "$TOTAL_REQUESTS" -gt 0 ]; then
    SUCCESS_RATE=$(echo "scale=2; ($SUCCESSFUL_REQUESTS * 100) / $TOTAL_REQUESTS" | bc -l 2>/dev/null || echo "N/A")
else
    SUCCESS_RATE="0"
fi

echo ""
echo "📊 Test Results:"
echo "  Total Requests: ${TOTAL_REQUESTS}"
echo "  Successful Requests: ${SUCCESSFUL_REQUESTS}"
echo "  Failed Requests: ${FAILED_REQUESTS}"
echo "  Success Rate: ${SUCCESS_RATE}%"
echo ""

# Check if metrics meet requirements
if [ "$SUCCESS_RATE" != "N/A" ]; then
    SUCCESS_CHECK=$(echo "$SUCCESS_RATE >= 99" | bc -l 2>/dev/null || echo "0")
    
    if [ "$SUCCESS_CHECK" = "1" ]; then
        echo -e "${GREEN}✅ Traffic test PASSED - Ready for pilot tenant activation${NC}"
        echo ""
        echo -e "${YELLOW}🎯 Next step: Enable pilot tenant${NC}"
        echo "Run: ./scripts/d3-enable-pilot.sh"
        
        # Generate summary report
        TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
        cat > /tmp/d3-traffic-report-simple.json << EOF
{
  "timestamp": "${TIMESTAMP}",
  "phase": "D3_SYNTHETIC_TRAFFIC_SIMPLE",
  "target": "${STAGING_URL}${MCP_ENDPOINT}",
  "configuration": {
    "rate": ${RATE},
    "total_requests": ${COUNT},
    "batch_size": ${BATCH_SIZE}
  },
  "results": {
    "total_requests": ${TOTAL_REQUESTS},
    "successful_requests": ${SUCCESSFUL_REQUESTS},
    "failed_requests": ${FAILED_REQUESTS},
    "success_rate": "${SUCCESS_RATE}%"
  },
  "requirements": {
    "min_success_rate": "99%"
  },
  "status": "PASSED"
}
EOF
        
        echo "📊 Detailed report: /tmp/d3-traffic-report-simple.json"
        
    else
        echo -e "${RED}❌ Traffic test FAILED - Metrics don't meet requirements${NC}"
        echo "Required: Success Rate ≥99%"
        echo "Actual: Success Rate ${SUCCESS_RATE}%"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  Could not analyze metrics. Please review results manually.${NC}"
fi

# Show sample response
echo ""
echo -e "${BLUE}📋 Sample Response:${NC}"
if [ -f "/tmp/response_1_1.json" ]; then
    echo "Response from first request:"
    cat /tmp/response_1_1.json | jq '.' 2>/dev/null || cat /tmp/response_1_1.json | head -10
fi

# Cleanup
rm -f /tmp/response_*.json

echo ""
echo -e "${GREEN}🎉 D3 Synthetic Traffic Phase Complete!${NC}"
