#!/bin/bash
# D3 Pre-Test: Verify MCP Rules Engine Setup
# Quick verification before running full synthetic traffic test

set -e

# Configuration
PROJECT_ID="texas-laws-personalinjury"
STAGING_SERVICE="mcp-staging"
REGION="us-central1"
PILOT_TENANT="pilot-smith"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔍 D3 Pre-Test: MCP Rules Engine Setup Verification${NC}"
echo "=================================================="
echo "Project: ${PROJECT_ID}"
echo "Service: ${STAGING_SERVICE}"
echo "Region: ${REGION}"
echo ""

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Not authenticated with gcloud. Please run: gcloud auth login${NC}"
    exit 1
fi

echo -e "${GREEN}✅ gcloud authenticated${NC}"

# Check if service exists
echo -e "${YELLOW}🔍 Checking staging service...${NC}"
if ! gcloud run services describe ${STAGING_SERVICE} --region=${REGION} --project=${PROJECT_ID} &>/dev/null; then
    echo -e "${RED}❌ Service ${STAGING_SERVICE} not found in region ${REGION}${NC}"
    echo "Available services:"
    gcloud run services list --project=${PROJECT_ID} --region=${REGION}
    exit 1
fi

# Get service URL
STAGING_URL=$(gcloud run services describe ${STAGING_SERVICE} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format="value(status.url)")

echo -e "${GREEN}✅ Service found: ${STAGING_URL}${NC}"

# Check MCP API key
echo -e "${YELLOW}🔑 Checking MCP API key...${NC}"
MCP_API_KEY=$(gcloud secrets versions access latest --secret="mcp-key-pilot-smith" --project="${PROJECT_ID}" 2>/dev/null || echo "")

if [ -z "$MCP_API_KEY" ] || [ "$MCP_API_KEY" = "PLACEHOLDER_KEY_TO_BE_ROTATED" ]; then
    echo -e "${RED}❌ MCP API key not found or not rotated${NC}"
    echo "Available secrets:"
    gcloud secrets list --project=${PROJECT_ID} | grep mcp
    exit 1
fi

echo -e "${GREEN}✅ MCP API key retrieved${NC}"

# Test service health
echo -e "${YELLOW}🏥 Testing service health...${NC}"
HEALTH_RESPONSE=$(curl -s -w "%{http_code}" "${STAGING_URL}/" -o /tmp/health_response.json)
HEALTH_CODE="${HEALTH_RESPONSE: -3}"

if [[ "$HEALTH_CODE" == "200" ]]; then
    echo -e "${GREEN}✅ Service health: OK${NC}"
else
    echo -e "${RED}❌ Service health check failed (HTTP ${HEALTH_CODE})${NC}"
    if [ -f "/tmp/health_response.json" ]; then
        echo "Response:"
        cat /tmp/health_response.json
    fi
    # Don't exit on health check failure for MCP service - it may not have a health endpoint
    echo -e "${YELLOW}⚠️  Continuing despite health check failure - MCP service may not have /health endpoint${NC}"
fi

# Test MCP service directly (skip feature flags for direct MCP service)
echo -e "${YELLOW}🚩 Testing MCP service directly...${NC}"
echo -e "${YELLOW}⚠️  Skipping feature flags test - testing MCP service directly${NC}"

# Test MCP endpoint
echo -e "${YELLOW}🧪 Testing MCP endpoint...${NC}"
MCP_RESPONSE=$(curl -s -w "%{http_code}" -X POST "${STAGING_URL}/mcp/run" \
    -H "Content-Type: application/json" \
    -H "X-API-Key: ${MCP_API_KEY}" \
    -H "User-Agent: D3-PreTest/1.0" \
    -d '{
        "toolName": "calculate_deadlines",
        "params": {
            "jurisdiction": "TX_STATE",
            "triggerCode": "SERVICE_OF_PROCESS",
            "startDate": "2025-07-01",
            "practiceArea": "personal_injury"
        }
    }' -o /tmp/mcp_response.json)
MCP_CODE="${MCP_RESPONSE: -3}"

if [[ "$MCP_CODE" == "200" ]]; then
    echo -e "${GREEN}✅ MCP endpoint: Working${NC}"
    if command -v jq &> /dev/null && [ -f "/tmp/mcp_response.json" ]; then
        DEADLINES_COUNT=$(jq -r '.result.deadlines | length // 0' /tmp/mcp_response.json 2>/dev/null || echo "0")
        echo "  Deadlines returned: ${DEADLINES_COUNT}"
        echo "  Sample response:"
        cat /tmp/mcp_response.json | jq '.' 2>/dev/null | head -10 || cat /tmp/mcp_response.json | head -5
    fi
elif [[ "$MCP_CODE" == "404" ]]; then
    echo -e "${YELLOW}⚠️  MCP endpoint not found - may not be implemented yet${NC}"
elif [[ "$MCP_CODE" == "403" ]] || [[ "$MCP_CODE" == "401" ]]; then
    echo -e "${YELLOW}⚠️  MCP endpoint access denied - check API key${NC}"
    if [ -f "/tmp/mcp_response.json" ]; then
        echo "Response:"
        cat /tmp/mcp_response.json | head -3
    fi
else
    echo -e "${YELLOW}⚠️  MCP endpoint returned HTTP ${MCP_CODE}${NC}"
    if [ -f "/tmp/mcp_response.json" ]; then
        echo "Response:"
        cat /tmp/mcp_response.json | head -5
    fi
fi

# Environment variables check
echo -e "${YELLOW}🔧 Checking environment variables...${NC}"
ENV_VARS=$(gcloud run services describe ${STAGING_SERVICE} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format="value(spec.template.spec.template.spec.containers[0].env[].name,spec.template.spec.template.spec.containers[0].env[].value)" \
    | grep -E "(MCP|FEATURE)" || echo "No MCP-related environment variables found")

echo "MCP-related environment variables:"
echo "${ENV_VARS}"

# Summary
echo ""
echo -e "${BLUE}📋 Pre-Test Summary:${NC}"
echo "  ✅ gcloud authenticated"
echo "  ✅ Staging service accessible"
echo "  ✅ MCP API key available"
echo "  ✅ Service health check passed"
echo "  ✅ Feature flags endpoint working"
echo "  ⚠️  MCP endpoint status: HTTP ${MCP_CODE}"
echo ""

if [[ "$HEALTH_CODE" == "200" && "$FLAGS_CODE" == "200" ]]; then
    echo -e "${GREEN}🎉 Pre-test PASSED - Ready for synthetic traffic test!${NC}"
    echo ""
    echo -e "${YELLOW}🎯 Next step: Run synthetic traffic test${NC}"
    echo "Run: ./scripts/d3-synthetic-traffic.sh"
else
    echo -e "${RED}❌ Pre-test FAILED - Please fix issues before proceeding${NC}"
    exit 1
fi

# Cleanup
rm -f /tmp/health_response.json /tmp/flags_response.json /tmp/mcp_response.json

echo ""
echo -e "${GREEN}✨ Pre-test complete!${NC}"
