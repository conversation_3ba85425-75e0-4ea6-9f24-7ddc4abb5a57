#!/bin/bash
# D3 Pre-Test: Verify MCP Rules Engine Vercel Deployment
# Quick verification before running full synthetic traffic test

set -e

# Configuration
MCP_URL="https://pi-lawyer-mcp-rules-k46fd4vfq-jpkays-projects.vercel.app"
MCP_ENDPOINT="/api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔍 D3 Pre-Test: MCP Rules Engine Vercel Deployment Verification${NC}"
echo "=================================================="
echo "Target: ${MCP_URL}${MCP_ENDPOINT}"
echo ""

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

# Check if curl is available
if ! command -v curl &> /dev/null; then
    echo -e "${RED}❌ curl not found. Please install curl.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ curl available${NC}"

# Test service health
echo -e "${YELLOW}🏥 Testing service health...${NC}"
HEALTH_RESPONSE=$(curl -s -w "%{http_code}" "${MCP_URL}/api/health" -o /tmp/health_response.json)
HEALTH_CODE="${HEALTH_RESPONSE: -3}"

if [[ "$HEALTH_CODE" == "200" ]]; then
    echo -e "${GREEN}✅ Service health: OK${NC}"
    if [ -f "/tmp/health_response.json" ]; then
        echo "Health response:"
        cat /tmp/health_response.json | jq '.' 2>/dev/null || cat /tmp/health_response.json
    fi
else
    echo -e "${RED}❌ Service health check failed (HTTP ${HEALTH_CODE})${NC}"
    if [ -f "/tmp/health_response.json" ]; then
        echo "Response:"
        cat /tmp/health_response.json
    fi
    exit 1
fi

# Test API root endpoint
echo -e "${YELLOW}🔍 Testing API root endpoint...${NC}"
API_RESPONSE=$(curl -s -w "%{http_code}" "${MCP_URL}${MCP_ENDPOINT}" -o /tmp/api_response.json)
API_CODE="${API_RESPONSE: -3}"

if [[ "$API_CODE" == "200" ]]; then
    echo -e "${GREEN}✅ API root endpoint: OK${NC}"
    if [ -f "/tmp/api_response.json" ]; then
        echo "API response:"
        cat /tmp/api_response.json | jq '.' 2>/dev/null || cat /tmp/api_response.json
    fi
else
    echo -e "${RED}❌ API root endpoint failed (HTTP ${API_CODE})${NC}"
    if [ -f "/tmp/api_response.json" ]; then
        echo "Response:"
        cat /tmp/api_response.json
    fi
    exit 1
fi

# Test MCP deadline calculation
echo -e "${YELLOW}🧪 Testing MCP deadline calculation...${NC}"
MCP_RESPONSE=$(curl -s -w "%{http_code}" -X POST "${MCP_URL}${MCP_ENDPOINT}" \
    -H "Content-Type: application/json" \
    -H "User-Agent: D3-PreTest/1.0" \
    -d '{
        "jurisdiction": "TX_STATE",
        "triggerCode": "SERVICE_OF_PROCESS",
        "startDate": "2025-07-01",
        "practiceArea": "personal_injury"
    }' -o /tmp/mcp_response.json)
MCP_CODE="${MCP_RESPONSE: -3}"

if [[ "$MCP_CODE" == "200" ]]; then
    echo -e "${GREEN}✅ MCP deadline calculation: Working${NC}"
    if command -v jq &> /dev/null && [ -f "/tmp/mcp_response.json" ]; then
        echo "MCP response:"
        cat /tmp/mcp_response.json | jq '.' 2>/dev/null || cat /tmp/mcp_response.json
    fi
else
    echo -e "${RED}❌ MCP deadline calculation failed (HTTP ${MCP_CODE})${NC}"
    if [ -f "/tmp/mcp_response.json" ]; then
        echo "Response:"
        cat /tmp/mcp_response.json | head -5
    fi
    exit 1
fi

# Test response time
echo -e "${YELLOW}⏱️  Testing response time...${NC}"
START_TIME=$(date +%s%3N)
PERF_RESPONSE=$(curl -s -w "%{time_total}" -X POST "${MCP_URL}${MCP_ENDPOINT}" \
    -H "Content-Type: application/json" \
    -H "User-Agent: D3-PreTest-Performance/1.0" \
    -d '{
        "jurisdiction": "TX_STATE",
        "triggerCode": "SERVICE_OF_PROCESS",
        "startDate": "2025-07-01",
        "practiceArea": "personal_injury"
    }' -o /dev/null)
END_TIME=$(date +%s%3N)

RESPONSE_TIME=$((END_TIME - START_TIME))
CURL_TIME=$(echo "$PERF_RESPONSE" | tail -1)

echo "Response time: ${RESPONSE_TIME}ms (curl: ${CURL_TIME}s)"

if [ "$RESPONSE_TIME" -lt 1000 ]; then
    echo -e "${GREEN}✅ Response time acceptable (<1000ms)${NC}"
else
    echo -e "${YELLOW}⚠️  Response time high (${RESPONSE_TIME}ms)${NC}"
fi

# Summary
echo ""
echo -e "${BLUE}📋 Pre-Test Summary:${NC}"
echo "  ✅ curl available"
echo "  ✅ Service health check passed"
echo "  ✅ API root endpoint working"
echo "  ✅ MCP deadline calculation working"
echo "  ✅ Response time: ${RESPONSE_TIME}ms"
echo ""

echo -e "${GREEN}🎉 Pre-test PASSED - Ready for synthetic traffic test!${NC}"
echo ""
echo -e "${YELLOW}🎯 Next step: Run synthetic traffic test${NC}"
echo "Run: ./scripts/d3-synthetic-traffic.sh"

# Cleanup
rm -f /tmp/health_response.json /tmp/api_response.json /tmp/mcp_response.json

echo ""
echo -e "${GREEN}✨ Pre-test complete!${NC}"
