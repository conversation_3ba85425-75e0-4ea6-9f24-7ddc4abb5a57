#!/bin/bash
# D3 Phase: Synthetic Traffic Burst for MCP Rules Engine
# Runs synthetic traffic against staging environment before enabling pilot tenant

set -e

# Configuration
# Note: We're testing the MCP Rules Engine Vercel deployment
STAGING_URL="https://pi-lawyer-mcp-rules-k46fd4vfq-jpkays-projects.vercel.app"
MCP_ENDPOINT="/api"
RATE=5
COUNT=500
PROJECT_ID="texas-laws-personalinjury"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Using direct Vercel URL - no need to query gcloud
echo -e "${YELLOW}🔍 Using MCP Rules Engine Vercel deployment...${NC}"

echo -e "${GREEN}🚀 D3 Phase: MCP Rules Engine Synthetic Traffic Test${NC}"
echo "=================================================="
echo "Target: ${STAGING_URL}${MCP_ENDPOINT}"
echo "Rate: ${RATE} req/sec"
echo "Count: ${COUNT} requests"
echo ""

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

# Check if curl and jq are available
if ! command -v curl &> /dev/null; then
    echo -e "${RED}❌ curl not found. Please install curl.${NC}"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}⚠️  jq not found. Installing via brew...${NC}"
    brew install jq || echo -e "${YELLOW}⚠️  Could not install jq. Some features may be limited.${NC}"
fi

# MCP service doesn't require authentication - skip API key check
echo -e "${YELLOW}🔑 MCP service uses public API - no authentication required${NC}"

echo -e "${GREEN}✅ Prerequisites checked${NC}"
echo ""

# Create test payload
echo -e "${YELLOW}📝 Creating test payload...${NC}"

TEST_PAYLOAD='{
  "jurisdiction": "TX_STATE",
  "triggerCode": "SERVICE_OF_PROCESS",
  "startDate": "2025-07-01",
  "practiceArea": "personal_injury"
}'

echo -e "${GREEN}✅ Test payload created${NC}"
echo ""

# Function to make a single request
make_request() {
    local start_time=$(date +%s)
    local response=$(curl -s -w "%{http_code}|%{time_total}" -X POST "${STAGING_URL}${MCP_ENDPOINT}" \
        -H "Content-Type: application/json" \
        -H "User-Agent: D3-SyntheticTraffic/1.0" \
        -d "${TEST_PAYLOAD}" \
        -o /tmp/response_$$.json)
    local end_time=$(date +%s)

    local http_code=$(echo "$response" | cut -d'|' -f1)
    local curl_time=$(echo "$response" | cut -d'|' -f2)
    local response_time=$((end_time - start_time))

    # Convert curl time to milliseconds (it's in seconds with decimal)
    local curl_time_ms=$(echo "$curl_time * 1000" | bc -l 2>/dev/null | cut -d'.' -f1)

    echo "${http_code},${curl_time_ms:-${response_time}000},$(date -u +"%Y-%m-%dT%H:%M:%SZ")"

    # Clean up temp file
    rm -f /tmp/response_$$.json
}

# Run the traffic test
echo -e "${YELLOW}🔥 Starting synthetic traffic burst...${NC}"
echo "Target: ${STAGING_URL}${MCP_ENDPOINT}"
echo "Rate: ${RATE} req/sec for ${COUNT} total requests"
echo "This will take approximately $((COUNT / RATE)) seconds..."
echo ""

# Initialize results file
echo "http_code,response_time_ms,timestamp" > /tmp/mcp-traffic-results.csv

# Run requests in batches
BATCH_SIZE=10
TOTAL_BATCHES=$((COUNT / BATCH_SIZE))
REQUESTS_SENT=0
SUCCESSFUL_REQUESTS=0
FAILED_REQUESTS=0
TOTAL_RESPONSE_TIME=0

echo -e "${BLUE}Progress: [                    ] 0%${NC}"

for ((batch=1; batch<=TOTAL_BATCHES; batch++)); do
    # Run batch of requests in parallel
    for ((i=1; i<=BATCH_SIZE; i++)); do
        make_request >> /tmp/mcp-traffic-results.csv &
    done

    # Wait for batch to complete
    wait

    # Update counters
    REQUESTS_SENT=$((REQUESTS_SENT + BATCH_SIZE))

    # Calculate progress
    PROGRESS=$((batch * 100 / TOTAL_BATCHES))
    PROGRESS_BAR=""
    for ((p=0; p<20; p++)); do
        if [ $((p * 5)) -lt $PROGRESS ]; then
            PROGRESS_BAR="${PROGRESS_BAR}█"
        else
            PROGRESS_BAR="${PROGRESS_BAR} "
        fi
    done

    echo -e "\r${BLUE}Progress: [${PROGRESS_BAR}] ${PROGRESS}%${NC}"

    # Rate limiting - wait between batches
    sleep $((BATCH_SIZE / RATE))
done

echo ""
echo -e "${GREEN}✅ Synthetic traffic test completed!${NC}"
echo ""

# Analyze results
echo -e "${YELLOW}📈 Analyzing results...${NC}"

# Parse CSV results
if [ -f "/tmp/mcp-traffic-results.csv" ]; then
    # Count successful and failed requests
    TOTAL_REQUESTS=$(tail -n +2 /tmp/mcp-traffic-results.csv | wc -l | tr -d ' ')
    SUCCESSFUL_REQUESTS=$(tail -n +2 /tmp/mcp-traffic-results.csv | awk -F',' '$1 == 200' | wc -l | tr -d ' ')
    FAILED_REQUESTS=$((TOTAL_REQUESTS - SUCCESSFUL_REQUESTS))

    # Calculate response time statistics
    if command -v awk &> /dev/null; then
        RESPONSE_TIMES=$(tail -n +2 /tmp/mcp-traffic-results.csv | awk -F',' '{print $2}' | sort -n)
        AVG_RESPONSE_TIME=$(echo "$RESPONSE_TIMES" | awk '{sum+=$1} END {if(NR>0) print sum/NR; else print 0}')
        P95_INDEX=$(echo "$TOTAL_REQUESTS * 0.95" | bc | cut -d'.' -f1)
        P95_LATENCY=$(echo "$RESPONSE_TIMES" | sed -n "${P95_INDEX}p")

        # Handle empty P95
        if [ -z "$P95_LATENCY" ]; then
            P95_LATENCY=$(echo "$RESPONSE_TIMES" | tail -1)
        fi
    else
        AVG_RESPONSE_TIME="N/A"
        P95_LATENCY="N/A"
    fi

    # Calculate success rate
    if [ "$TOTAL_REQUESTS" -gt 0 ]; then
        SUCCESS_RATE=$(echo "scale=2; ($SUCCESSFUL_REQUESTS * 100) / $TOTAL_REQUESTS" | bc -l 2>/dev/null || echo "N/A")
    else
        SUCCESS_RATE="0"
    fi

    echo ""
    echo "📊 Test Results:"
    echo "  Total Requests: ${TOTAL_REQUESTS}"
    echo "  Successful Requests: ${SUCCESSFUL_REQUESTS}"
    echo "  Failed Requests: ${FAILED_REQUESTS}"
    echo "  Success Rate: ${SUCCESS_RATE}%"
    echo "  Average Response Time: ${AVG_RESPONSE_TIME}ms"
    echo "  P95 Latency: ${P95_LATENCY}ms"
    echo ""

    # Check if metrics meet requirements
    if [ "$SUCCESS_RATE" != "N/A" ] && [ "$P95_LATENCY" != "N/A" ]; then
        SUCCESS_CHECK=$(echo "$SUCCESS_RATE >= 99" | bc -l 2>/dev/null || echo "0")
        LATENCY_CHECK=$(echo "$P95_LATENCY <= 700" | bc -l 2>/dev/null || echo "0")

        if [ "$SUCCESS_CHECK" = "1" ] && [ "$LATENCY_CHECK" = "1" ]; then
            echo -e "${GREEN}✅ Traffic test PASSED - Ready for pilot tenant activation${NC}"
            echo ""
            echo -e "${YELLOW}🎯 Next step: Enable pilot-smith tenant${NC}"
            echo "Run: ./scripts/d3-enable-pilot.sh"
        else
            echo -e "${RED}❌ Traffic test FAILED - Metrics don't meet requirements${NC}"
            echo "Required: Success Rate ≥99%, P95 Latency ≤700ms"
            echo "Actual: Success Rate ${SUCCESS_RATE}%, P95 Latency ${P95_LATENCY}ms"
            exit 1
        fi
    else
        echo -e "${YELLOW}⚠️  Could not fully analyze metrics. Please review results manually.${NC}"
        echo "Results file: /tmp/mcp-traffic-results.csv"
    fi

    # Generate summary report
    TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    cat > /tmp/d3-traffic-report.json << EOF
{
  "timestamp": "${TIMESTAMP}",
  "phase": "D3_SYNTHETIC_TRAFFIC",
  "target": "${STAGING_URL}${MCP_ENDPOINT}",
  "configuration": {
    "rate": ${RATE},
    "total_requests": ${COUNT},
    "batch_size": ${BATCH_SIZE}
  },
  "results": {
    "total_requests": ${TOTAL_REQUESTS},
    "successful_requests": ${SUCCESSFUL_REQUESTS},
    "failed_requests": ${FAILED_REQUESTS},
    "success_rate": "${SUCCESS_RATE}%",
    "average_response_time_ms": "${AVG_RESPONSE_TIME}",
    "p95_latency_ms": "${P95_LATENCY}"
  },
  "requirements": {
    "min_success_rate": "99%",
    "max_p95_latency": "700ms"
  }
}
EOF

    echo "📊 Detailed report: /tmp/d3-traffic-report.json"
    echo "📊 Raw results: /tmp/mcp-traffic-results.csv"
else
    echo -e "${RED}❌ No results file found${NC}"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 D3 Synthetic Traffic Phase Complete!${NC}"
