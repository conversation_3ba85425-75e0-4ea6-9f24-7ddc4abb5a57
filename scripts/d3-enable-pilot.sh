#!/bin/bash
# D3 Phase: Enable Pilot Tenant for MCP Rules Engine
# Activates MCP Rules Engine for pilot-smith tenant after successful traffic test

set -e

# Configuration
PROJECT_ID="texas-laws-personalinjury"
STAGING_SERVICE="mcp-staging"
REGION="us-central1"
PILOT_TENANT="pilot-smith"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🎯 D3 Phase: Enable Pilot Tenant (${PILOT_TENANT})${NC}"
echo "=================================================="
echo "Project: ${PROJECT_ID}"
echo "Service: ${STAGING_SERVICE}"
echo "Region: ${REGION}"
echo "Pilot Tenant: ${PILOT_TENANT}"
echo ""

# Check prerequisites
echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

# Check if gcloud is authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Not authenticated with gcloud. Please run: gcloud auth login${NC}"
    exit 1
fi

# Check if the service exists
if ! gcloud run services describe ${STAGING_SERVICE} --region=${REGION} --project=${PROJECT_ID} &>/dev/null; then
    echo -e "${RED}❌ Service ${STAGING_SERVICE} not found in region ${REGION}${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites checked${NC}"
echo ""

# Get current environment variables
echo -e "${YELLOW}🔍 Checking current feature flag status...${NC}"

CURRENT_ENV=$(gcloud run services describe ${STAGING_SERVICE} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format="value(spec.template.spec.template.spec.containers[0].env[].name,spec.template.spec.template.spec.containers[0].env[].value)" \
    | grep -E "(FEATURE_MCP_RULES_ENGINE|D3_PILOT_TENANT)" || true)

echo "Current environment variables:"
echo "${CURRENT_ENV}"
echo ""

# Confirm activation
echo -e "${BLUE}⚠️  This will enable MCP Rules Engine for pilot tenant: ${PILOT_TENANT}${NC}"
echo -e "${BLUE}   Real deadline calculations will be available in the UI.${NC}"
echo ""
read -p "Continue with pilot tenant activation? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Aborted."
    exit 1
fi

# Update environment variables to enable D3 pilot mode
echo -e "${YELLOW}🚀 Enabling D3 pilot tenant mode...${NC}"

gcloud run services update ${STAGING_SERVICE} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --update-env-vars="NEXT_PUBLIC_FEATURE_MCP_RULES_ENGINE=true,NEXT_PUBLIC_D3_PILOT_TENANT=true,FEATURE_MCP_RULES_ENGINE=true" \
    --quiet

echo -e "${GREEN}✅ Environment variables updated${NC}"
echo ""

# Wait for deployment to complete
echo -e "${YELLOW}⏳ Waiting for deployment to complete...${NC}"
sleep 10

# Get the service URL
SERVICE_URL=$(gcloud run services describe ${STAGING_SERVICE} \
    --region=${REGION} \
    --project=${PROJECT_ID} \
    --format="value(status.url)")

echo -e "${GREEN}✅ Service deployed at: ${SERVICE_URL}${NC}"
echo ""

# Verify feature flags are active
echo -e "${YELLOW}🔍 Verifying feature flag activation...${NC}"

# Test the feature flags endpoint
FEATURE_FLAGS_RESPONSE=$(curl -s "${SERVICE_URL}/api/admin/feature-flags?tenantId=${PILOT_TENANT}" \
    -H "Content-Type: application/json" \
    -H "User-Agent: D3-PilotActivation/1.0" || echo "ERROR")

if [[ "$FEATURE_FLAGS_RESPONSE" == "ERROR" ]]; then
    echo -e "${RED}❌ Failed to verify feature flags${NC}"
    exit 1
fi

echo "Feature flags response:"
echo "${FEATURE_FLAGS_RESPONSE}" | jq '.' 2>/dev/null || echo "${FEATURE_FLAGS_RESPONSE}"
echo ""

# Check if MCP is enabled for pilot tenant
MCP_ENABLED=$(echo "${FEATURE_FLAGS_RESPONSE}" | jq -r '.featureFlags.MCP_RULES_ENGINE // false' 2>/dev/null || echo "false")

if [[ "$MCP_ENABLED" == "true" ]]; then
    echo -e "${GREEN}✅ MCP Rules Engine successfully enabled for pilot tenant${NC}"
else
    echo -e "${RED}❌ MCP Rules Engine not enabled for pilot tenant${NC}"
    echo "Response: ${FEATURE_FLAGS_RESPONSE}"
    exit 1
fi

echo ""

# Test MCP endpoint with pilot tenant context
echo -e "${YELLOW}🧪 Testing MCP endpoint with pilot tenant...${NC}"

# Get MCP API key
MCP_API_KEY=$(gcloud secrets versions access latest --secret="mcp-key-pilot-smith" --project="${PROJECT_ID}")

# Test deadline calculation
TEST_RESPONSE=$(curl -s -X POST "${SERVICE_URL}/api/mcp/calculate-deadlines" \
    -H "Content-Type: application/json" \
    -H "X-Tenant-ID: ${PILOT_TENANT}" \
    -H "User-Agent: D3-PilotTest/1.0" \
    -d '{
        "jurisdiction": "TX_STATE",
        "triggerCode": "SERVICE_OF_PROCESS",
        "startDate": "2025-07-01",
        "practiceArea": "personal_injury"
    }' || echo "ERROR")

if [[ "$TEST_RESPONSE" == "ERROR" ]]; then
    echo -e "${YELLOW}⚠️  MCP endpoint test failed - this may be expected if endpoint is not yet implemented${NC}"
else
    echo "MCP test response:"
    echo "${TEST_RESPONSE}" | jq '.' 2>/dev/null || echo "${TEST_RESPONSE}"
fi

echo ""

# Record baseline metrics
echo -e "${YELLOW}📊 Recording baseline metrics...${NC}"

TIMESTAMP=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
BASELINE_FILE="/tmp/d3-pilot-baseline-${TIMESTAMP}.json"

cat > "${BASELINE_FILE}" << EOF
{
  "timestamp": "${TIMESTAMP}",
  "phase": "D3_PILOT_ACTIVATION",
  "tenant": "${PILOT_TENANT}",
  "service_url": "${SERVICE_URL}",
  "feature_flags": ${FEATURE_FLAGS_RESPONSE},
  "mcp_enabled": ${MCP_ENABLED},
  "notes": "Pilot tenant activated - monitor for 24 hours before D4 expansion"
}
EOF

echo -e "${GREEN}✅ Baseline metrics recorded: ${BASELINE_FILE}${NC}"
echo ""

# Success summary
echo -e "${GREEN}🎉 D3 Pilot Tenant Activation Complete!${NC}"
echo ""
echo -e "${BLUE}📋 Summary:${NC}"
echo "  ✅ Feature flags enabled for staging environment"
echo "  ✅ D3_PILOT_TENANT mode activated"
echo "  ✅ MCP Rules Engine enabled for ${PILOT_TENANT}"
echo "  ✅ Baseline metrics recorded"
echo ""
echo -e "${YELLOW}🎯 Next Steps:${NC}"
echo "  1. Test deadline calculation in ${PILOT_TENANT} tenant UI"
echo "  2. Monitor metrics for 24 hours"
echo "  3. Gather lawyer feedback"
echo "  4. Run key rotation test (Day 5)"
echo "  5. Proceed to D4 expansion if metrics are green"
echo ""
echo -e "${BLUE}📊 Monitoring:${NC}"
echo "  - Service URL: ${SERVICE_URL}"
echo "  - Feature Flags: ${SERVICE_URL}/api/admin/feature-flags?tenantId=${PILOT_TENANT}"
echo "  - Cloud Monitoring: https://console.cloud.google.com/monitoring"
echo ""
echo -e "${GREEN}✨ Pilot tenant is now live with MCP Rules Engine!${NC}"
