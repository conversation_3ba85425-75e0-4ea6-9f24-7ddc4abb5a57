# Tenant Onboarding Automation

This document explains the automated MCP (Model Context Protocol) key provisioning system that automatically creates and configures API keys for new tenants when they are created in the system.

## Overview

The automated tenant onboarding system eliminates manual MCP API key provisioning by automatically:

1. **Detecting new tenants** with `mcp_status: 'pending_key'`
2. **Creating tenant-specific API keys** via Google API Keys service
3. **Storing keys securely** in Google Secret Manager
4. **Configuring access permissions** for AiLex service accounts
5. **Updating tenant records** with secret paths and active status

## Architecture

```mermaid
graph TD
    A[New Tenant Created] --> B{mcp_status = 'pending_key'?}
    B -->|Yes| C[Cloud Function Triggered]
    B -->|No| D[Skip Processing]
    
    C --> E[Create API Key]
    E --> F[Store in Secret Manager]
    F --> G[Grant Service Account Access]
    G --> H[Update Tenant Record]
    H --> I[Tenant Active]
    
    E -->|Error| J[Update Status: Failed]
    F -->|Error| J
    G -->|Error| J
    H -->|Error| J
```

## Components

### 1. Cloud Function: `onTenantCreate`

**Location**: `cloud-functions/onTenantCreate.ts`

**Trigger**: Firestore document creation on `/tenants/{tenantId}`

**Conditions**: Only processes tenants with `mcp_status: 'pending_key'`

**Actions**:
- Creates API key via `@google-cloud/apikeys`
- Stores key in Secret Manager via `@google-cloud/secret-manager`
- Grants access to AiLex service accounts
- Updates tenant document with secret path and `active` status

### 2. Database Schema

**Table**: `tenants.firms`

**New Fields**:
```sql
-- Google Secret Manager path for tenant-specific MCP API key
mcp_secret_path TEXT

-- Status of MCP key provisioning
mcp_status TEXT DEFAULT 'pending_key' 
CHECK (mcp_status IN ('pending_key', 'active', 'inactive', 'key_provisioning_failed'))
```

### 3. Terraform Infrastructure

**Location**: `infra/terraform/mcp_onboard.tf`

**Resources**:
- Cloud Function deployment
- Service account with required IAM roles
- Cloud Storage bucket for function source
- IAM bindings for API Keys and Secret Manager access

### 4. Integration Tests

**Location**: `tests/onboard.e2e.spec.ts`

**Test Coverage**:
- Successful key provisioning flow
- Error handling and recovery
- Concurrent tenant creation
- Deadlines tool integration

## Environment Variables

### Required for Cloud Function

```bash
# Google Cloud Project ID
GOOGLE_CLOUD_PROJECT=texas-laws-personalinjury

# Environment (production, staging, development)
NODE_ENV=production
```

### Required for Backend Services

```bash
# MCP Rules Engine base URL
MCP_RULES_BASE=https://rules.ailexlaw.com

# Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# Redis for caching
REDIS_URL=redis://localhost:6379
```

## Service Accounts

### Cloud Function Service Account
- **Name**: `tenant-onboard-function@PROJECT_ID.iam.gserviceaccount.com`
- **Roles**:
  - `roles/apikeys.admin` - Create and manage API keys
  - `roles/secretmanager.admin` - Create and manage secrets
  - `roles/datastore.user` - Read/write Firestore documents
  - `roles/run.invoker` - Invoke Cloud Run services

### AiLex Service Accounts (Secret Access)
- **Main**: `main-jp@PROJECT_ID.iam.gserviceaccount.com`
- **MCP Client**: `mcp-client@PROJECT_ID.iam.gserviceaccount.com`
- **Role**: `roles/secretmanager.secretAccessor`

## Deployment

### 1. Deploy Infrastructure

```bash
cd infra/terraform
terraform init
terraform plan
terraform apply
```

### 2. Deploy Cloud Function

The function is automatically deployed via Terraform, but can be manually deployed:

```bash
cd cloud-functions
npm install
npm run build
firebase deploy --only functions:onTenantCreate
```

### 3. Apply Database Migration

```bash
# Apply the migration to add MCP fields
supabase db push
```

## Usage

### Automatic Provisioning

When a new tenant is created with `mcp_status: 'pending_key'`, the system automatically:

1. **Creates the tenant record**:
```sql
INSERT INTO tenants.firms (name, mcp_status, ...) 
VALUES ('New Law Firm', 'pending_key', ...);
```

2. **Cloud Function triggers** within seconds

3. **Tenant becomes active** with provisioned API key:
```sql
-- After successful provisioning
SELECT mcp_status, mcp_secret_path FROM tenants.firms WHERE id = 'tenant-id';
-- Result: 'active', 'projects/PROJECT_ID/secrets/mcp-key-tenant-id'
```

### Manual Provisioning (Fallback)

If automatic provisioning fails, manually trigger:

```bash
# Re-trigger by updating status
UPDATE tenants.firms 
SET mcp_status = 'pending_key' 
WHERE id = 'tenant-id';
```

## Monitoring

### Cloud Function Logs

```bash
# View function logs
gcloud functions logs read onTenantCreate --limit 50

# Monitor in real-time
gcloud functions logs tail onTenantCreate
```

### Tenant Status Monitoring

```sql
-- Check provisioning status
SELECT 
  id,
  name,
  mcp_status,
  mcp_secret_path IS NOT NULL as has_secret,
  created_at,
  updated_at
FROM tenants.firms 
ORDER BY created_at DESC;

-- Count by status
SELECT mcp_status, COUNT(*) 
FROM tenants.firms 
GROUP BY mcp_status;
```

## Troubleshooting

### Common Issues

1. **Function timeout**: Increase timeout in `mcp_onboard.tf`
2. **Permission denied**: Verify service account IAM roles
3. **API quota exceeded**: Check Google Cloud quotas
4. **Secret already exists**: Handle duplicate tenant IDs

### Error Recovery

Failed provisioning sets `mcp_status: 'key_provisioning_failed'`:

```sql
-- Retry failed provisioning
UPDATE tenants.firms 
SET mcp_status = 'pending_key' 
WHERE mcp_status = 'key_provisioning_failed';
```

### Testing

```bash
# Run integration tests
npm test -- jest.config.onboard.js

# Test specific scenario
npm test -- tests/onboard.e2e.spec.ts -t "should automatically provision MCP key"
```

## Security Considerations

1. **Secret Access**: Only authorized service accounts can access secrets
2. **API Key Restrictions**: Keys are scoped to MCP Rules Engine API
3. **Audit Logging**: All operations are logged in Cloud Audit Logs
4. **Encryption**: Secrets are encrypted at rest and in transit
5. **Rotation**: Keys can be rotated via scheduled Cloud Function

## Development Mode

To disable automatic provisioning in development:

```bash
# Set environment variable
export DISABLE_AUTO_PROVISIONING=true

# Or modify Cloud Function trigger condition
```

## Key Rotation

Future enhancement: Automated key rotation via Cloud Scheduler:

```bash
# Monthly rotation job (planned)
gcloud scheduler jobs create http rotate-mcp-keys \
  --schedule="0 0 1 * *" \
  --uri="https://REGION-PROJECT_ID.cloudfunctions.net/rotateMcpKeys"
```

## Integration with Existing Systems

### Deadlines Tool Integration

The `deadlinesTool` automatically uses tenant-specific API keys:

```typescript
// Backend: agents/interactive/deadline/tools/deadlinesTool.ts
async function getMcpApiKey(): Promise<string> {
  const tenantContext = getCurrentTenantContext();
  const secretPath = tenantContext.mcpSecretPath;

  if (!secretPath) {
    throw new Error('Tenant MCP key not provisioned');
  }

  const [version] = await secretManagerClient.accessSecretVersion({
    name: `${secretPath}/versions/latest`,
  });

  return version.payload?.data?.toString() || '';
}
```

### Frontend Integration

Tenant status is displayed in admin dashboards:

```typescript
// Frontend: components/admin/TenantStatus.tsx
interface TenantStatusProps {
  tenant: Tenant;
}

export function TenantStatus({ tenant }: TenantStatusProps) {
  const mcpStatusBadge = {
    'pending_key': { color: 'yellow', text: 'Provisioning...' },
    'active': { color: 'green', text: 'Active' },
    'inactive': { color: 'gray', text: 'Inactive' },
    'key_provisioning_failed': { color: 'red', text: 'Failed' },
  };

  return (
    <Badge color={mcpStatusBadge[tenant.mcp_status].color}>
      {mcpStatusBadge[tenant.mcp_status].text}
    </Badge>
  );
}
```

## Performance Considerations

- **Function Cold Start**: ~2-3 seconds for first invocation
- **Provisioning Time**: ~5-10 seconds total per tenant
- **Concurrent Limit**: 10 concurrent function instances
- **API Rate Limits**: Google API Keys service limits apply
- **Secret Manager**: 10,000 operations/minute limit

## Cost Estimation

Per tenant provisioning costs:
- **Cloud Function**: ~$0.0001 per invocation
- **Secret Manager**: $0.06 per secret per month
- **API Keys**: Free tier (10,000 requests/month)
- **Total**: ~$0.06 per tenant per month

## Compliance and Auditing

All operations are logged for compliance:

```bash
# View audit logs
gcloud logging read "resource.type=cloud_function AND resource.labels.function_name=onTenantCreate"

# Export logs for compliance
gcloud logging read "resource.type=cloud_function" --format=json > audit-logs.json
```

## Disaster Recovery

### Backup Strategy
- **Secrets**: Automatically replicated across regions
- **Function Code**: Stored in Cloud Storage with versioning
- **Database**: Supabase automatic backups

### Recovery Procedures
1. **Function Failure**: Redeploy via Terraform
2. **Secret Loss**: Re-provision via manual trigger
3. **Database Corruption**: Restore from Supabase backup

## Future Enhancements

1. **Multi-region Deployment**: Deploy functions across regions
2. **Advanced Monitoring**: Custom metrics and alerting
3. **Key Rotation**: Automated monthly key rotation
4. **Batch Provisioning**: Handle multiple tenants simultaneously
5. **Webhook Integration**: Notify external systems of provisioning events
