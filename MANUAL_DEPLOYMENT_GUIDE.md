# Manual Deployment Guide for Automated Tenant Onboarding

Due to IAM permission limitations with the current service account, this guide provides step-by-step instructions for manually deploying the automated tenant onboarding system.

## Prerequisites ✅

- [x] **Database Migration Applied**: MCP fields added to `tenants.firms` table
- [x] **Cloud Function Code Ready**: `cloud-functions/onTenantCreate.ts` implemented
- [x] **Terraform Configuration**: Infrastructure code prepared
- [x] **Google Cloud Project**: `texas-laws-personalinjury` configured

## Step 1: Enable Required APIs

In the Google Cloud Console, enable these APIs:

```bash
# Navigate to: https://console.cloud.google.com/apis/library
# Enable the following APIs:

1. Cloud Functions API (cloudfunctions.googleapis.com)
2. Secret Manager API (secretmanager.googleapis.com) 
3. API Keys API (apikeys.googleapis.com)
4. Firestore API (firestore.googleapis.com)
5. Cloud Build API (cloudbuild.googleapis.com)
```

## Step 2: Create Service Account

1. **Navigate to IAM & Admin > Service Accounts**
2. **Create Service Account**:
   - Name: `tenant-onboard-function`
   - Description: `Service account for automated tenant MCP key provisioning`

3. **Grant Roles**:
   - `API Keys Admin` (roles/apikeys.admin)
   - `Secret Manager Admin` (roles/secretmanager.admin)
   - `Cloud Datastore User` (roles/datastore.user)
   - `Cloud Run Invoker` (roles/run.invoker)

## Step 3: Create Secret Manager Secrets

For each existing tenant, create a secret:

1. **Navigate to Secret Manager**
2. **Create Secret**:
   - Name: `mcp-key-pilot-smith`
   - Value: `PLACEHOLDER_TO_BE_REPLACED`
   - Labels:
     - `tenant: pilot-smith`
     - `service: mcp-rules-engine`
     - `managed_by: manual`

3. **Repeat for other tenants**:
   - `mcp-key-demo-tenant`
   - Add more as needed

## Step 4: Grant Secret Access

For each secret created:

1. **Click on the secret name**
2. **Go to Permissions tab**
3. **Add Principal**:
   - `<EMAIL>`
   - Role: `Secret Manager Secret Accessor`
4. **Add another Principal**:
   - `<EMAIL>`
   - Role: `Secret Manager Secret Accessor`

## Step 5: Deploy Cloud Function

### Option A: Using Google Cloud Console

1. **Navigate to Cloud Functions**
2. **Create Function**:
   - Name: `tenant-onboard`
   - Region: `us-central1`
   - Trigger Type: `Eventarc`
   - Event Provider: `Cloud Firestore`
   - Event Type: `Document Create`
   - Document Path: `tenants/{tenantId}`

3. **Runtime Settings**:
   - Runtime: `Node.js 20`
   - Entry Point: `onTenantCreate`
   - Memory: `256 MiB`
   - Timeout: `30 seconds`
   - Service Account: `<EMAIL>`

4. **Environment Variables**:
   ```
   GOOGLE_CLOUD_PROJECT=texas-laws-personalinjury
   NODE_ENV=production
   ```

5. **Source Code**: Upload the contents of `cloud-functions/` directory

### Option B: Using gcloud CLI

```bash
# Navigate to cloud-functions directory
cd cloud-functions

# Install dependencies
npm install

# Deploy function
gcloud functions deploy tenant-onboard \
  --gen2 \
  --runtime=nodejs20 \
  --region=us-central1 \
  --source=. \
  --entry-point=onTenantCreate \
  --trigger-event-filters="type=google.cloud.firestore.document.v1.created" \
  --trigger-event-filters="database=(default)" \
  --trigger-event-filters-path-pattern="document=tenants/{tenantId}" \
  --service-account=<EMAIL> \
  --set-env-vars="GOOGLE_CLOUD_PROJECT=texas-laws-personalinjury,NODE_ENV=production" \
  --memory=256Mi \
  --timeout=30s
```

## Step 6: Create API Keys for Existing Tenants

1. **Navigate to APIs & Services > Credentials**
2. **Create Credentials > API Key**:
   - Name: `MCP Rules Engine API Key - pilot-smith`
   - Restrictions: None (or restrict to specific APIs if needed)

3. **Copy the API Key**
4. **Update Secret Manager**:
   - Go to `mcp-key-pilot-smith` secret
   - Create new version with the actual API key
   - Delete the placeholder version

5. **Repeat for other tenants**

## Step 7: Update Tenant Records

Update existing tenant records to have the correct secret paths:

```sql
-- Connect to Supabase database and run:
UPDATE tenants.firms 
SET 
  mcp_secret_path = 'projects/texas-laws-personalinjury/secrets/mcp-key-pilot-smith',
  mcp_status = 'active'
WHERE name = 'Pilot Smith Law Firm';

-- Repeat for other tenants with their respective secret paths
```

## Step 8: Test the System

### Create a Test Tenant

1. **Using Supabase SQL Editor**:
```sql
INSERT INTO tenants.firms (
  tenant_id,
  name,
  state_bar_number,
  firm_type,
  primary_email,
  phone,
  address,
  mcp_status
) VALUES (
  gen_random_uuid(),
  'Test Automated Firm',
  'TX999999',
  'Solo Practice',
  '<EMAIL>',
  '555-0123',
  '{"street": "123 Test St", "city": "Austin", "state": "TX", "zip": "78701"}',
  'pending_key'
);
```

2. **Create Firestore Document** (to trigger the function):
```javascript
// In Firestore console, create document in 'tenants' collection
{
  "id": "test-tenant-123",
  "name": "Test Automated Firm",
  "status": "pending_key",
  "created_at": new Date()
}
```

### Verify Function Execution

1. **Check Cloud Function Logs**:
   - Navigate to Cloud Functions > tenant-onboard > Logs
   - Look for successful execution logs

2. **Check Secret Manager**:
   - Verify new secret `mcp-key-test-tenant-123` was created

3. **Check Firestore**:
   - Verify tenant document was updated with `status: 'active'`

## Step 9: Monitor and Validate

### Check Function Health
```bash
# View function logs
gcloud functions logs read tenant-onboard --limit=50

# Check function status
gcloud functions describe tenant-onboard --region=us-central1
```

### Verify Database State
```sql
-- Check tenant MCP status
SELECT 
  id,
  name,
  mcp_status,
  mcp_secret_path,
  created_at
FROM tenants.firms
ORDER BY created_at DESC
LIMIT 10;

-- Count by status
SELECT mcp_status, COUNT(*) 
FROM tenants.firms 
GROUP BY mcp_status;
```

## Troubleshooting

### Common Issues

1. **Function Not Triggering**:
   - Check Firestore trigger configuration
   - Verify document path pattern: `tenants/{tenantId}`
   - Check function permissions

2. **Permission Errors**:
   - Verify service account has required roles
   - Check IAM bindings on secrets

3. **API Key Creation Fails**:
   - Ensure API Keys API is enabled
   - Check service account permissions

### Logs and Monitoring

- **Function Logs**: Cloud Functions > tenant-onboard > Logs
- **Audit Logs**: IAM & Admin > Audit Logs
- **Error Reporting**: Operations > Error Reporting

## Success Criteria

✅ **Database Migration**: MCP fields added to tenants.firms
✅ **Cloud Function**: Deployed and responding to Firestore triggers  
✅ **Secret Manager**: Secrets created and accessible
✅ **API Keys**: Generated and stored securely
✅ **Automation**: New tenants automatically get MCP keys
✅ **Integration**: Deadlines tool can access tenant-specific keys

## Next Steps

1. **Monitor the system** for 24-48 hours
2. **Create additional test tenants** to verify automation
3. **Set up alerting** for function failures
4. **Implement key rotation** (future enhancement)
5. **Scale to production** with real tenant onboarding

The automated tenant onboarding system is now ready! 🚀
