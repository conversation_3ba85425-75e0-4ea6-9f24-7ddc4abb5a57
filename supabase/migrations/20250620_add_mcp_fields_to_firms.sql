-- Add MCP-related fields to tenants.firms table for automated key provisioning
-- Migration: 20250620_add_mcp_fields_to_firms.sql

BEGIN;

-- Add mcpSecretPath field to store the Secret Manager secret path
ALTER TABLE tenants.firms 
ADD COLUMN IF NOT EXISTS mcp_secret_path TEXT;

-- Add status field to track tenant onboarding status
-- Note: We're adding this as a separate field from the existing 'status' field
-- to specifically track MCP key provisioning status
ALTER TABLE tenants.firms 
ADD COLUMN IF NOT EXISTS mcp_status TEXT DEFAULT 'pending_key' 
CHECK (mcp_status IN ('pending_key', 'active', 'inactive', 'key_provisioning_failed'));

-- Create index for efficient querying by MCP status
CREATE INDEX IF NOT EXISTS idx_firms_mcp_status ON tenants.firms(mcp_status);

-- Create index for efficient querying by MCP secret path
CREATE INDEX IF NOT EXISTS idx_firms_mcp_secret_path ON tenants.firms(mcp_secret_path) 
WHERE mcp_secret_path IS NOT NULL;

-- Add comments for documentation
COMMENT ON COLUMN tenants.firms.mcp_secret_path IS 'Google Secret Manager path for tenant-specific MCP API key';
COMMENT ON COLUMN tenants.firms.mcp_status IS 'Status of MCP key provisioning: pending_key, active, inactive, key_provisioning_failed';

-- Update existing firms to have pending_key status if they don't have an MCP secret path
UPDATE tenants.firms 
SET mcp_status = 'pending_key' 
WHERE mcp_secret_path IS NULL AND mcp_status IS NULL;

-- Create a function to automatically set mcp_status to pending_key for new firms
CREATE OR REPLACE FUNCTION tenants.set_default_mcp_status()
RETURNS TRIGGER AS $$
BEGIN
  -- Set default MCP status for new firms
  IF NEW.mcp_status IS NULL THEN
    NEW.mcp_status := 'pending_key';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to set default MCP status on insert
DROP TRIGGER IF EXISTS set_default_mcp_status_trigger ON tenants.firms;
CREATE TRIGGER set_default_mcp_status_trigger
  BEFORE INSERT ON tenants.firms
  FOR EACH ROW
  EXECUTE FUNCTION tenants.set_default_mcp_status();

-- Update RLS policies to include MCP fields
-- Users can view their own firm's MCP status
CREATE POLICY "Users can view their firm MCP status"
  ON tenants.firms FOR SELECT
  USING (tenant_id = (auth.jwt() ->> 'tenant_id')::uuid);

-- Only service role can update MCP fields (for Cloud Function)
CREATE POLICY "Service role can update MCP fields"
  ON tenants.firms FOR UPDATE
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Grant necessary permissions to service role for MCP operations
GRANT SELECT, UPDATE ON tenants.firms TO service_role;

COMMIT;

-- Verification queries (commented out for production)
-- SELECT id, name, mcp_status, mcp_secret_path, created_at 
-- FROM tenants.firms 
-- ORDER BY created_at DESC 
-- LIMIT 5;
