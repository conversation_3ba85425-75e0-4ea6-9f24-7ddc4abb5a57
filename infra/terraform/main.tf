# MCP Rules Engine Infrastructure Configuration
# Main Terraform configuration for MCP Rules Engine integration

terraform {
  required_version = ">= 1.0"
  
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

# Configure the Google Cloud Provider
provider "google" {
  project = var.project_id
  region  = var.region
}

# Variables
variable "project_id" {
  description = "Google Cloud Project ID"
  type        = string
  default     = "new-texas-laws"
}

variable "tenants" {
  description = "List of tenant IDs to create secrets for"
  type        = list(string)
  default     = ["pilot-smith", "demo-tenant"]
}

variable "region" {
  description = "Google Cloud region"
  type        = string
  default     = "us-central1"
}
