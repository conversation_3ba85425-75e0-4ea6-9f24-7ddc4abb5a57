# MCP Tenant Onboarding Infrastructure - Cross-Project Architecture
#
# Architecture:
# - Cloud Function deploys in new-texas-laws (close to Supabase trigger)
# - API Keys and Secrets created in texas-laws-personalinjury (where MCP Gateway runs)
# - Uses service account impersonation for cross-project access

# Variables for cross-project setup
variable "tenant_project" {
  description = "Project where tenants and Cloud Function live"
  type        = string
  default     = "new-texas-laws"
}

variable "mcp_project" {
  description = "Project where MCP Gateway and secrets live"
  type        = string
  default     = "texas-laws-personalinjury"
}

# Configure providers for both projects
provider "google" {
  alias   = "tenant_project"
  project = var.tenant_project
  region  = var.region
}

provider "google" {
  alias   = "mcp_project"
  project = var.mcp_project
  region  = var.region
}

# Enable required APIs in tenant project (new-texas-laws)
resource "google_project_service" "tenant_cloudfunctions" {
  provider = google.tenant_project
  service  = "cloudfunctions.googleapis.com"

  disable_dependent_services = true
  disable_on_destroy         = false
}

resource "google_project_service" "tenant_cloudbuild" {
  provider = google.tenant_project
  service  = "cloudbuild.googleapis.com"

  disable_dependent_services = true
  disable_on_destroy         = false
}

resource "google_project_service" "tenant_firestore" {
  provider = google.tenant_project
  service  = "firestore.googleapis.com"

  disable_dependent_services = true
  disable_on_destroy         = false
}

resource "google_project_service" "tenant_iam" {
  provider = google.tenant_project
  service  = "iam.googleapis.com"

  disable_dependent_services = true
  disable_on_destroy         = false
}

# Enable required APIs in MCP project (texas-laws-personalinjury)
resource "google_project_service" "mcp_apikeys" {
  provider = google.mcp_project
  service  = "apikeys.googleapis.com"

  disable_dependent_services = true
  disable_on_destroy         = false
}

resource "google_project_service" "mcp_secretmanager" {
  provider = google.mcp_project
  service  = "secretmanager.googleapis.com"

  disable_dependent_services = true
  disable_on_destroy         = false
}

resource "google_project_service" "mcp_iam" {
  provider = google.mcp_project
  service  = "iam.googleapis.com"

  disable_dependent_services = true
  disable_on_destroy         = false
}

# Step 1: Create service account in MCP project for API key and secret management
resource "google_service_account" "mcp_key_provisioner" {
  provider     = google.mcp_project
  account_id   = "mcp-key-provisioner"
  display_name = "MCP Key Provisioner Service Account"
  description  = "Cross-project service account for creating API keys and secrets in texas-laws-personalinjury"
}

# Grant API Keys and Secret Manager admin roles to the provisioner SA in MCP project
resource "google_project_iam_member" "provisioner_apikeys_admin" {
  provider = google.mcp_project
  project  = var.mcp_project
  role     = "roles/apikeys.admin"
  member   = "serviceAccount:${google_service_account.mcp_key_provisioner.email}"
}

resource "google_project_iam_member" "provisioner_secretmanager_admin" {
  provider = google.mcp_project
  project  = var.mcp_project
  role     = "roles/secretmanager.admin"
  member   = "serviceAccount:${google_service_account.mcp_key_provisioner.email}"
}

# Step 2: Create service account for the Cloud Function in tenant project
resource "google_service_account" "tenant_onboard_function" {
  provider     = google.tenant_project
  account_id   = "tenant-onboard-function"
  display_name = "Tenant Onboarding Cloud Function Service Account"
  description  = "Service account for automated tenant MCP key provisioning Cloud Function"
}

# Grant Firestore access to the Cloud Function SA in tenant project
resource "google_project_iam_member" "function_firestore_user" {
  provider = google.tenant_project
  project  = var.tenant_project
  role     = "roles/datastore.user"
  member   = "serviceAccount:${google_service_account.tenant_onboard_function.email}"
}

resource "google_project_iam_member" "function_run_invoker" {
  provider = google.tenant_project
  project  = var.tenant_project
  role     = "roles/run.invoker"
  member   = "serviceAccount:${google_service_account.tenant_onboard_function.email}"
}

# Step 3: Grant Cloud Function SA permission to impersonate the MCP provisioner SA
resource "google_service_account_iam_member" "allow_impersonation" {
  provider           = google.mcp_project
  service_account_id = google_service_account.mcp_key_provisioner.name
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${google_service_account.tenant_onboard_function.email}"
}

# Create Cloud Storage bucket for function source code in tenant project
resource "google_storage_bucket" "function_source" {
  provider = google.tenant_project
  name     = "${var.tenant_project}-tenant-onboard-functions"
  location = var.region

  uniform_bucket_level_access = true

  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type = "Delete"
    }
  }

  versioning {
    enabled = true
  }
}

# Create ZIP archive of the Cloud Function source code
data "archive_file" "function_source" {
  type        = "zip"
  output_path = "${path.module}/../../cloud-functions/function-source.zip"
  source_dir  = "${path.module}/../../cloud-functions"
  excludes = [
    "node_modules",
    "lib",
    "*.zip",
    ".git",
    "*.log"
  ]
}

# Upload function source to Cloud Storage in tenant project
resource "google_storage_bucket_object" "function_source" {
  provider = google.tenant_project
  name     = "tenant-onboard-${data.archive_file.function_source.output_md5}.zip"
  bucket   = google_storage_bucket.function_source.name
  source   = data.archive_file.function_source.output_path

  depends_on = [data.archive_file.function_source]
}

# Deploy the Cloud Function in tenant project
resource "google_cloudfunctions2_function" "tenant_onboard" {
  provider    = google.tenant_project
  name        = "tenant-onboard"
  location    = var.region
  description = "Automatically provisions MCP API keys for new tenants via cross-project impersonation"

  build_config {
    runtime     = "nodejs20"
    entry_point = "onTenantCreate"

    source {
      storage_source {
        bucket = google_storage_bucket.function_source.name
        object = google_storage_bucket_object.function_source.name
      }
    }
  }

  service_config {
    max_instance_count    = 10
    min_instance_count    = 0
    available_memory      = "256Mi"
    timeout_seconds       = 30
    service_account_email = google_service_account.tenant_onboard_function.email

    environment_variables = {
      TENANT_PROJECT                = var.tenant_project
      MCP_PROJECT                   = var.mcp_project
      MCP_KEY_PROVISIONER_SA        = google_service_account.mcp_key_provisioner.email
      NODE_ENV                      = terraform.workspace
    }
  }

  event_trigger {
    trigger_region = var.region
    event_type     = "google.cloud.firestore.document.v1.created"
    retry_policy   = "RETRY_POLICY_RETRY"

    event_filters {
      attribute = "database"
      value     = "(default)"
    }

    event_filters {
      attribute = "document"
      value     = "tenants/{tenantId}"
    }
  }

  depends_on = [
    google_project_service.tenant_cloudfunctions,
    google_project_service.tenant_cloudbuild,
    google_project_service.tenant_firestore,
    google_storage_bucket_object.function_source,
    google_service_account_iam_member.allow_impersonation,
  ]
}

# Grant the Cloud Function permission to be invoked by Firestore
resource "google_cloud_run_service_iam_member" "function_invoker" {
  provider = google.tenant_project
  location = google_cloudfunctions2_function.tenant_onboard.location
  service  = google_cloudfunctions2_function.tenant_onboard.name
  role     = "roles/run.invoker"
  member   = "serviceAccount:${google_service_account.tenant_onboard_function.email}"
}

# Output the cross-project configuration details
output "tenant_onboard_function_name" {
  description = "Name of the tenant onboarding Cloud Function"
  value       = google_cloudfunctions2_function.tenant_onboard.name
}

output "tenant_onboard_function_url" {
  description = "URL of the tenant onboarding Cloud Function"
  value       = google_cloudfunctions2_function.tenant_onboard.service_config[0].uri
}

output "tenant_project_service_account" {
  description = "Email of the tenant onboarding function service account (in new-texas-laws)"
  value       = google_service_account.tenant_onboard_function.email
}

output "mcp_project_service_account" {
  description = "Email of the MCP key provisioner service account (in texas-laws-personalinjury)"
  value       = google_service_account.mcp_key_provisioner.email
}

output "cross_project_setup_summary" {
  description = "Summary of the cross-project setup"
  value = {
    tenant_project = var.tenant_project
    mcp_project    = var.mcp_project
    function_sa    = google_service_account.tenant_onboard_function.email
    provisioner_sa = google_service_account.mcp_key_provisioner.email
    secret_path_pattern = "projects/${var.mcp_project}/secrets/mcp-key-{tenant-id}"
  }
}
