# MCP Tenant Onboarding Infrastructure
# Deploys Cloud Function for automated tenant MCP key provisioning

# Enable required APIs
resource "google_project_service" "cloudfunctions" {
  service = "cloudfunctions.googleapis.com"
  
  disable_dependent_services = true
  disable_on_destroy         = false
}

resource "google_project_service" "cloudbuild" {
  service = "cloudbuild.googleapis.com"
  
  disable_dependent_services = true
  disable_on_destroy         = false
}

resource "google_project_service" "firestore" {
  service = "firestore.googleapis.com"
  
  disable_dependent_services = true
  disable_on_destroy         = false
}

# Create service account for the Cloud Function
resource "google_service_account" "tenant_onboard_function" {
  account_id   = "tenant-onboard-function"
  display_name = "Tenant Onboarding Cloud Function Service Account"
  description  = "Service account for automated tenant MCP key provisioning"
}

# Grant necessary IAM roles to the Cloud Function service account
resource "google_project_iam_member" "function_apikeys_admin" {
  project = var.project_id
  role    = "roles/apikeys.admin"
  member  = "serviceAccount:${google_service_account.tenant_onboard_function.email}"
}

resource "google_project_iam_member" "function_secretmanager_admin" {
  project = var.project_id
  role    = "roles/secretmanager.admin"
  member  = "serviceAccount:${google_service_account.tenant_onboard_function.email}"
}

resource "google_project_iam_member" "function_firestore_user" {
  project = var.project_id
  role    = "roles/datastore.user"
  member  = "serviceAccount:${google_service_account.tenant_onboard_function.email}"
}

resource "google_project_iam_member" "function_run_invoker" {
  project = var.project_id
  role    = "roles/run.invoker"
  member  = "serviceAccount:${google_service_account.tenant_onboard_function.email}"
}

# Create Cloud Storage bucket for function source code
resource "google_storage_bucket" "function_source" {
  name     = "${var.project_id}-tenant-onboard-functions"
  location = var.region
  
  uniform_bucket_level_access = true
  
  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type = "Delete"
    }
  }
  
  versioning {
    enabled = true
  }
}

# Create ZIP archive of the Cloud Function source code
data "archive_file" "function_source" {
  type        = "zip"
  output_path = "${path.module}/../../cloud-functions/function-source.zip"
  source_dir  = "${path.module}/../../cloud-functions"
  excludes = [
    "node_modules",
    "lib",
    "*.zip",
    ".git",
    "*.log"
  ]
}

# Upload function source to Cloud Storage
resource "google_storage_bucket_object" "function_source" {
  name   = "tenant-onboard-${data.archive_file.function_source.output_md5}.zip"
  bucket = google_storage_bucket.function_source.name
  source = data.archive_file.function_source.output_path
  
  depends_on = [data.archive_file.function_source]
}

# Deploy the Cloud Function
resource "google_cloudfunctions2_function" "tenant_onboard" {
  name        = "tenant-onboard"
  location    = var.region
  description = "Automatically provisions MCP API keys for new tenants"

  build_config {
    runtime     = "nodejs20"
    entry_point = "onTenantCreate"
    
    source {
      storage_source {
        bucket = google_storage_bucket.function_source.name
        object = google_storage_bucket_object.function_source.name
      }
    }
  }

  service_config {
    max_instance_count    = 10
    min_instance_count    = 0
    available_memory      = "256Mi"
    timeout_seconds       = 30
    service_account_email = google_service_account.tenant_onboard_function.email
    
    environment_variables = {
      GOOGLE_CLOUD_PROJECT = var.project_id
      NODE_ENV            = terraform.workspace
    }
  }

  event_trigger {
    trigger_region = var.region
    event_type     = "google.cloud.firestore.document.v1.created"
    retry_policy   = "RETRY_POLICY_RETRY"
    
    event_filters {
      attribute = "database"
      value     = "(default)"
    }
    
    event_filters {
      attribute = "document"
      value     = "tenants/{tenantId}"
    }
  }

  depends_on = [
    google_project_service.cloudfunctions,
    google_project_service.cloudbuild,
    google_project_service.firestore,
    google_storage_bucket_object.function_source,
  ]
}

# Grant the Cloud Function permission to be invoked by Firestore
resource "google_cloud_run_service_iam_member" "function_invoker" {
  location = google_cloudfunctions2_function.tenant_onboard.location
  service  = google_cloudfunctions2_function.tenant_onboard.name
  role     = "roles/run.invoker"
  member   = "serviceAccount:${google_service_account.tenant_onboard_function.email}"
}

# Output the function details
output "tenant_onboard_function_name" {
  description = "Name of the tenant onboarding Cloud Function"
  value       = google_cloudfunctions2_function.tenant_onboard.name
}

output "tenant_onboard_function_url" {
  description = "URL of the tenant onboarding Cloud Function"
  value       = google_cloudfunctions2_function.tenant_onboard.service_config[0].uri
}

output "tenant_onboard_service_account" {
  description = "Email of the tenant onboarding function service account"
  value       = google_service_account.tenant_onboard_function.email
}
