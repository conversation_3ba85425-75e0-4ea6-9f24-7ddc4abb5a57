/**
 * Test script to manually trigger the tenant onboarding function
 */

const { onTenantCreate } = require('./lib/onTenantCreate');

// Mock CloudEvent data that simulates a Firestore document creation
const mockCloudEvent = {
  data: {
    value: {
      name: 'projects/texas-laws-personalinjury/databases/(default)/documents/tenants/test-tenant-123',
      fields: {
        name: { stringValue: 'Test Tenant Firm' },
        status: { stringValue: 'pending_key' },
        created_at: { timestampValue: '2025-06-19T18:40:00Z' }
      },
      createTime: '2025-06-19T18:40:00Z',
      updateTime: '2025-06-19T18:40:00Z'
    }
  },
  type: 'google.cloud.firestore.document.v1.created',
  source: 'projects/texas-laws-personalinjury/databases/(default)',
  subject: 'documents/tenants/test-tenant-123'
};

async function testFunction() {
  try {
    console.log('Testing tenant onboarding function...');
    console.log('Mock event:', JSON.stringify(mockCloudEvent, null, 2));
    
    await onTenantCreate(mockCloudEvent);
    
    console.log('Function executed successfully!');
  } catch (error) {
    console.error('Function execution failed:', error);
  }
}

testFunction();
