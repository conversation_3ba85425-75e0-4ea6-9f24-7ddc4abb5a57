/**
 * <PERSON><PERSON><PERSON> to create a test tenant document in Firestore
 * This will trigger the Cloud Function to create API keys and secrets
 */

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
admin.initializeApp({
  projectId: 'texas-laws-personalinjury'
});

const db = admin.firestore();

async function createTestTenant() {
  try {
    const tenantId = `test-tenant-${Date.now()}`;
    const tenantData = {
      name: 'Automated Test Tenant Firm',
      status: 'pending_key',
      created_at: admin.firestore.FieldValue.serverTimestamp(),
      test: true
    };

    console.log(`Creating test tenant: ${tenantId}`);
    console.log('Tenant data:', tenantData);

    // Create the document in the tenants collection
    await db.collection('tenants').doc(tenantId).set(tenantData);

    console.log(`✅ Test tenant created successfully: ${tenantId}`);
    console.log('This should trigger the Cloud Function to create API key and secret.');
    console.log('Check the function logs with: gcloud functions logs read tenant-onboard --limit=10');

    // Wait a moment for the function to execute
    console.log('Waiting 10 seconds for function to execute...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Check if the document was updated with the secret path
    const updatedDoc = await db.collection('tenants').doc(tenantId).get();
    if (updatedDoc.exists) {
      const data = updatedDoc.data();
      console.log('Updated tenant data:', data);
      
      if (data.mcp_secret_path) {
        console.log('✅ SUCCESS: MCP secret path was added:', data.mcp_secret_path);
      } else {
        console.log('⚠️  MCP secret path not yet added. Check function logs for any errors.');
      }
    }

  } catch (error) {
    console.error('❌ Error creating test tenant:', error);
  } finally {
    process.exit(0);
  }
}

createTestTenant();
