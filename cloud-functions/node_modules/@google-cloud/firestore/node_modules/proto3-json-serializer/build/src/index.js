"use strict";
// Copyright 2021 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.fromProto3JSON = exports.toProto3JSON = void 0;
var toproto3json_1 = require("./toproto3json");
Object.defineProperty(exports, "toProto3JSON", { enumerable: true, get: function () { return toproto3json_1.toProto3JSON; } });
var fromproto3json_1 = require("./fromproto3json");
Object.defineProperty(exports, "fromProto3JSON", { enumerable: true, get: function () { return fromproto3json_1.fromProto3JSON; } });
//# sourceMappingURL=index.js.map