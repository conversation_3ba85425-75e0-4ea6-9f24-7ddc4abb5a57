"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by synthtool. **
// ** https://github.com/googleapis/synthtool **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.protos = exports.ApiKeysClient = exports.v2 = void 0;
const v2 = require("./v2");
exports.v2 = v2;
const ApiKeysClient = v2.ApiKeysClient;
exports.ApiKeysClient = ApiKeysClient;
exports.default = { v2, ApiKeysClient };
const protos = require("../protos/protos");
exports.protos = protos;
//# sourceMappingURL=index.js.map