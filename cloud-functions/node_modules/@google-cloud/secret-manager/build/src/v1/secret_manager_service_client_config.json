{"interfaces": {"google.cloud.secretmanager.v1.SecretManagerService": {"retry_codes": {"non_idempotent": [], "idempotent": ["DEADLINE_EXCEEDED", "UNAVAILABLE"], "resource_exhausted_unavailable": ["RESOURCE_EXHAUSTED", "UNAVAILABLE"]}, "retry_params": {"default": {"initial_retry_delay_millis": 100, "retry_delay_multiplier": 1.3, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}, "dd71b8c1d82d51c18ce5ba426d9acc4c44e7746e": {"initial_retry_delay_millis": 2000, "retry_delay_multiplier": 2, "max_retry_delay_millis": 60000, "initial_rpc_timeout_millis": 60000, "rpc_timeout_multiplier": 1, "max_rpc_timeout_millis": 60000, "total_timeout_millis": 600000}}, "methods": {"ListSecrets": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "CreateSecret": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "AddSecretVersion": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetSecret": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "UpdateSecret": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DeleteSecret": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "ListSecretVersions": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetSecretVersion": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "AccessSecretVersion": {"timeout_millis": 60000, "retry_codes_name": "resource_exhausted_unavailable", "retry_params_name": "dd71b8c1d82d51c18ce5ba426d9acc4c44e7746e"}, "DisableSecretVersion": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "EnableSecretVersion": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "DestroySecretVersion": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "SetIamPolicy": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "GetIamPolicy": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}, "TestIamPermissions": {"timeout_millis": 60000, "retry_codes_name": "non_idempotent", "retry_params_name": "default"}}}}}