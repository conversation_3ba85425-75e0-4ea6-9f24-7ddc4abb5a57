"use strict";
// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.SecretManagerServiceClient = void 0;
const jsonProtos = require("../../protos/protos.json");
/**
 * Client JSON configuration object, loaded from
 * `src/v1/secret_manager_service_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./secret_manager_service_client_config.json");
const version = require('../../../package.json').version;
/**
 *  Secret Manager Service
 *
 *  Manages secrets and operations using those secrets. Implements a REST
 *  model with the following objects:
 *
 *  * {@link protos.google.cloud.secretmanager.v1.Secret|Secret}
 *  * {@link protos.google.cloud.secretmanager.v1.SecretVersion|SecretVersion}
 * @class
 * @memberof v1
 */
class SecretManagerServiceClient {
    /**
     * Construct an instance of SecretManagerServiceClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://developers.google.com/identity/protocols/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new SecretManagerServiceClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        var _a, _b, _c, _d, _e;
        this._terminated = false;
        this.descriptors = {
            page: {},
            stream: {},
            longrunning: {},
            batching: {},
        };
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if ((opts === null || opts === void 0 ? void 0 : opts.universe_domain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universeDomain) &&
            (opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== (opts === null || opts === void 0 ? void 0 : opts.universeDomain)) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            (_c = (_b = (_a = opts === null || opts === void 0 ? void 0 : opts.universeDomain) !== null && _a !== void 0 ? _a : opts === null || opts === void 0 ? void 0 : opts.universe_domain) !== null && _b !== void 0 ? _b : universeDomainEnvVar) !== null && _c !== void 0 ? _c : 'googleapis.com';
        this._servicePath = 'secretmanager.' + this._universeDomain;
        const servicePath = (opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint) || this._servicePath;
        this._providedCustomServicePath = !!((opts === null || opts === void 0 ? void 0 : opts.servicePath) || (opts === null || opts === void 0 ? void 0 : opts.apiEndpoint));
        const port = (opts === null || opts === void 0 ? void 0 : opts.port) || staticMembers.port;
        const clientConfig = (_d = opts === null || opts === void 0 ? void 0 : opts.clientConfig) !== null && _d !== void 0 ? _d : {};
        const fallback = (_e = opts === null || opts === void 0 ? void 0 : opts.fallback) !== null && _e !== void 0 ? _e : (typeof window !== 'undefined' && typeof (window === null || window === void 0 ? void 0 : window.fetch) === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        this.locationsClient = new this._gaxModule.LocationsClient(this._gaxGrpc, opts);
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            projectPathTemplate: new this._gaxModule.PathTemplate('projects/{project}'),
            projectLocationSecretPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/secrets/{secret}'),
            projectLocationSecretSecretVersionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/secrets/{secret}/versions/{secret_version}'),
            projectSecretPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/secrets/{secret}'),
            projectSecretSecretVersionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/secrets/{secret}/versions/{secret_version}'),
            topicPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/topics/{topic}'),
            secretPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/secrets/{secret}'),
            secretVersionPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/secrets/{secret}/versions/{secret_version}'),
        };
        // Some of the methods on this service return "paged" results,
        // (e.g. 50 results at a time, with tokens to get subsequent
        // pages). Denote the keys used for pagination and results.
        this.descriptors.page = {
            listSecrets: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'secrets'),
            listSecretVersions: new this._gaxModule.PageDescriptor('pageToken', 'nextPageToken', 'versions'),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.cloud.secretmanager.v1.SecretManagerService', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.secretManagerServiceStub) {
            return this.secretManagerServiceStub;
        }
        // Put together the "service stub" for
        // google.cloud.secretmanager.v1.SecretManagerService.
        this.secretManagerServiceStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.cloud.secretmanager.v1.SecretManagerService')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.cloud.secretmanager.v1
                    .SecretManagerService, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const secretManagerServiceStubMethods = [
            'listSecrets',
            'createSecret',
            'addSecretVersion',
            'getSecret',
            'updateSecret',
            'deleteSecret',
            'listSecretVersions',
            'getSecretVersion',
            'accessSecretVersion',
            'disableSecretVersion',
            'enableSecretVersion',
            'destroySecretVersion',
            'setIamPolicy',
            'getIamPolicy',
            'testIamPermissions',
        ];
        for (const methodName of secretManagerServiceStubMethods) {
            const callPromise = this.secretManagerServiceStub.then(stub => (...args) => {
                if (this._terminated) {
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.page[methodName] || undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.secretManagerServiceStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'secretmanager.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'secretmanager.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return ['https://www.googleapis.com/auth/cloud-platform'];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    createSecret(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.createSecret(request, options, callback);
    }
    addSecretVersion(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.addSecretVersion(request, options, callback);
    }
    getSecret(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getSecret(request, options, callback);
    }
    updateSecret(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                'secret.name': (_a = request.secret.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.updateSecret(request, options, callback);
    }
    deleteSecret(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.deleteSecret(request, options, callback);
    }
    getSecretVersion(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getSecretVersion(request, options, callback);
    }
    accessSecretVersion(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.accessSecretVersion(request, options, callback);
    }
    disableSecretVersion(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.disableSecretVersion(request, options, callback);
    }
    enableSecretVersion(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.enableSecretVersion(request, options, callback);
    }
    destroySecretVersion(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                name: (_a = request.name) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.destroySecretVersion(request, options, callback);
    }
    setIamPolicy(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                resource: (_a = request.resource) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.setIamPolicy(request, options, callback);
    }
    getIamPolicy(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                resource: (_a = request.resource) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.getIamPolicy(request, options, callback);
    }
    testIamPermissions(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                resource: (_a = request.resource) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.testIamPermissions(request, options, callback);
    }
    listSecrets(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listSecrets(request, options, callback);
    }
    /**
     * Equivalent to `method.name.toCamelCase()`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project associated with the
     *   {@link protos.google.cloud.secretmanager.v1.Secret|Secrets}, in the format `projects/*`
     *   or `projects/* /locations/*`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of results to be returned in a single page. If
     *   set to 0, the server decides the number of results to return. If the
     *   number is greater than 25000, it is capped at 25000.
     * @param {string} [request.pageToken]
     *   Optional. Pagination token, returned earlier via
     *   {@link protos.google.cloud.secretmanager.v1.ListSecretsResponse.next_page_token|ListSecretsResponse.next_page_token}.
     * @param {string} [request.filter]
     *   Optional. Filter string, adhering to the rules in
     *   [List-operation
     *   filtering](https://cloud.google.com/secret-manager/docs/filtering). List
     *   only secrets matching the filter. If filter is empty, all secrets are
     *   listed.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.secretmanager.v1.Secret|Secret} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listSecretsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listSecretsStream(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listSecrets'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listSecrets.createStream(this.innerApiCalls.listSecrets, request, callSettings);
    }
    /**
     * Equivalent to `listSecrets`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the project associated with the
     *   {@link protos.google.cloud.secretmanager.v1.Secret|Secrets}, in the format `projects/*`
     *   or `projects/* /locations/*`
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of results to be returned in a single page. If
     *   set to 0, the server decides the number of results to return. If the
     *   number is greater than 25000, it is capped at 25000.
     * @param {string} [request.pageToken]
     *   Optional. Pagination token, returned earlier via
     *   {@link protos.google.cloud.secretmanager.v1.ListSecretsResponse.next_page_token|ListSecretsResponse.next_page_token}.
     * @param {string} [request.filter]
     *   Optional. Filter string, adhering to the rules in
     *   [List-operation
     *   filtering](https://cloud.google.com/secret-manager/docs/filtering). List
     *   only secrets matching the filter. If filter is empty, all secrets are
     *   listed.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.secretmanager.v1.Secret|Secret}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/secret_manager_service.list_secrets.js</caption>
     * region_tag:secretmanager_v1_generated_SecretManagerService_ListSecrets_async
     */
    listSecretsAsync(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listSecrets'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listSecrets.asyncIterate(this.innerApiCalls['listSecrets'], request, callSettings);
    }
    listSecretVersions(request, optionsOrCallback, callback) {
        var _a;
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        this.initialize();
        return this.innerApiCalls.listSecretVersions(request, options, callback);
    }
    /**
     * Equivalent to `method.name.toCamelCase()`, but returns a NodeJS Stream object.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the
     *   {@link protos.google.cloud.secretmanager.v1.Secret|Secret} associated with the
     *   {@link protos.google.cloud.secretmanager.v1.SecretVersion|SecretVersions} to list, in
     *   the format `projects/* /secrets/*` or `projects/* /locations/* /secrets/*`.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of results to be returned in a single page. If
     *   set to 0, the server decides the number of results to return. If the
     *   number is greater than 25000, it is capped at 25000.
     * @param {string} [request.pageToken]
     *   Optional. Pagination token, returned earlier via
     *   ListSecretVersionsResponse.next_page_token][].
     * @param {string} [request.filter]
     *   Optional. Filter string, adhering to the rules in
     *   [List-operation
     *   filtering](https://cloud.google.com/secret-manager/docs/filtering). List
     *   only secret versions matching the filter. If filter is empty, all secret
     *   versions are listed.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which emits an object representing {@link protos.google.cloud.secretmanager.v1.SecretVersion|SecretVersion} on 'data' event.
     *   The client library will perform auto-pagination by default: it will call the API as many
     *   times as needed. Note that it can affect your quota.
     *   We recommend using `listSecretVersionsAsync()`
     *   method described below for async iteration which you can stop as needed.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     */
    listSecretVersionsStream(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listSecretVersions'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listSecretVersions.createStream(this.innerApiCalls.listSecretVersions, request, callSettings);
    }
    /**
     * Equivalent to `listSecretVersions`, but returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.parent
     *   Required. The resource name of the
     *   {@link protos.google.cloud.secretmanager.v1.Secret|Secret} associated with the
     *   {@link protos.google.cloud.secretmanager.v1.SecretVersion|SecretVersions} to list, in
     *   the format `projects/* /secrets/*` or `projects/* /locations/* /secrets/*`.
     * @param {number} [request.pageSize]
     *   Optional. The maximum number of results to be returned in a single page. If
     *   set to 0, the server decides the number of results to return. If the
     *   number is greater than 25000, it is capped at 25000.
     * @param {string} [request.pageToken]
     *   Optional. Pagination token, returned earlier via
     *   ListSecretVersionsResponse.next_page_token][].
     * @param {string} [request.filter]
     *   Optional. Filter string, adhering to the rules in
     *   [List-operation
     *   filtering](https://cloud.google.com/secret-manager/docs/filtering). List
     *   only secret versions matching the filter. If filter is empty, all secret
     *   versions are listed.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link protos.google.cloud.secretmanager.v1.SecretVersion|SecretVersion}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1/secret_manager_service.list_secret_versions.js</caption>
     * region_tag:secretmanager_v1_generated_SecretManagerService_ListSecretVersions_async
     */
    listSecretVersionsAsync(request, options) {
        var _a;
        request = request || {};
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        options.otherArgs.headers['x-goog-request-params'] =
            this._gaxModule.routingHeader.fromParams({
                parent: (_a = request.parent) !== null && _a !== void 0 ? _a : '',
            });
        const defaultCallSettings = this._defaults['listSecretVersions'];
        const callSettings = defaultCallSettings.merge(options);
        this.initialize();
        return this.descriptors.page.listSecretVersions.asyncIterate(this.innerApiCalls['listSecretVersions'], request, callSettings);
    }
    /**
     * Gets information about a location.
     *
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   Resource name for the location.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html | CallOptions} for more details.
     * @returns {Promise} - The promise which resolves to an array.
     *   The first element of the array is an object representing {@link google.cloud.location.Location | Location}.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#regular-methods | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const [response] = await client.getLocation(request);
     * ```
     */
    getLocation(request, options, callback) {
        return this.locationsClient.getLocation(request, options, callback);
    }
    /**
     * Lists information about the supported locations for this service. Returns an iterable object.
     *
     * `for`-`await`-`of` syntax is used with the iterable to get response elements on-demand.
     * @param {Object} request
     *   The request object that will be sent.
     * @param {string} request.name
     *   The resource that owns the locations collection, if applicable.
     * @param {string} request.filter
     *   The standard list filter.
     * @param {number} request.pageSize
     *   The standard list page size.
     * @param {string} request.pageToken
     *   The standard list page token.
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Object}
     *   An iterable Object that allows {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Iteration_protocols | async iteration }.
     *   When you iterate the returned iterable, each element will be an object representing
     *   {@link google.cloud.location.Location | Location}. The API will be called under the hood as needed, once per the page,
     *   so you can stop the iteration when you don't need more results.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#auto-pagination | documentation }
     *   for more details and examples.
     * @example
     * ```
     * const iterable = client.listLocationsAsync(request);
     * for await (const response of iterable) {
     *   // process response
     * }
     * ```
     */
    listLocationsAsync(request, options) {
        return this.locationsClient.listLocationsAsync(request, options);
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified project resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    projectPath(project) {
        return this.pathTemplates.projectPathTemplate.render({
            project: project,
        });
    }
    /**
     * Parse the project from Project resource.
     *
     * @param {string} projectName
     *   A fully-qualified path representing Project resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectName(projectName) {
        return this.pathTemplates.projectPathTemplate.match(projectName).project;
    }
    /**
     * Return a fully-qualified projectLocationSecret resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} secret
     * @returns {string} Resource name string.
     */
    projectLocationSecretPath(project, location, secret) {
        return this.pathTemplates.projectLocationSecretPathTemplate.render({
            project: project,
            location: location,
            secret: secret,
        });
    }
    /**
     * Parse the project from ProjectLocationSecret resource.
     *
     * @param {string} projectLocationSecretName
     *   A fully-qualified path representing project_location_secret resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationSecretName(projectLocationSecretName) {
        return this.pathTemplates.projectLocationSecretPathTemplate.match(projectLocationSecretName).project;
    }
    /**
     * Parse the location from ProjectLocationSecret resource.
     *
     * @param {string} projectLocationSecretName
     *   A fully-qualified path representing project_location_secret resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationSecretName(projectLocationSecretName) {
        return this.pathTemplates.projectLocationSecretPathTemplate.match(projectLocationSecretName).location;
    }
    /**
     * Parse the secret from ProjectLocationSecret resource.
     *
     * @param {string} projectLocationSecretName
     *   A fully-qualified path representing project_location_secret resource.
     * @returns {string} A string representing the secret.
     */
    matchSecretFromProjectLocationSecretName(projectLocationSecretName) {
        return this.pathTemplates.projectLocationSecretPathTemplate.match(projectLocationSecretName).secret;
    }
    /**
     * Return a fully-qualified projectLocationSecretSecretVersion resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} secret
     * @param {string} secret_version
     * @returns {string} Resource name string.
     */
    projectLocationSecretSecretVersionPath(project, location, secret, secretVersion) {
        return this.pathTemplates.projectLocationSecretSecretVersionPathTemplate.render({
            project: project,
            location: location,
            secret: secret,
            secret_version: secretVersion,
        });
    }
    /**
     * Parse the project from ProjectLocationSecretSecretVersion resource.
     *
     * @param {string} projectLocationSecretSecretVersionName
     *   A fully-qualified path representing project_location_secret_secret_version resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectLocationSecretSecretVersionName(projectLocationSecretSecretVersionName) {
        return this.pathTemplates.projectLocationSecretSecretVersionPathTemplate.match(projectLocationSecretSecretVersionName).project;
    }
    /**
     * Parse the location from ProjectLocationSecretSecretVersion resource.
     *
     * @param {string} projectLocationSecretSecretVersionName
     *   A fully-qualified path representing project_location_secret_secret_version resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromProjectLocationSecretSecretVersionName(projectLocationSecretSecretVersionName) {
        return this.pathTemplates.projectLocationSecretSecretVersionPathTemplate.match(projectLocationSecretSecretVersionName).location;
    }
    /**
     * Parse the secret from ProjectLocationSecretSecretVersion resource.
     *
     * @param {string} projectLocationSecretSecretVersionName
     *   A fully-qualified path representing project_location_secret_secret_version resource.
     * @returns {string} A string representing the secret.
     */
    matchSecretFromProjectLocationSecretSecretVersionName(projectLocationSecretSecretVersionName) {
        return this.pathTemplates.projectLocationSecretSecretVersionPathTemplate.match(projectLocationSecretSecretVersionName).secret;
    }
    /**
     * Parse the secret_version from ProjectLocationSecretSecretVersion resource.
     *
     * @param {string} projectLocationSecretSecretVersionName
     *   A fully-qualified path representing project_location_secret_secret_version resource.
     * @returns {string} A string representing the secret_version.
     */
    matchSecretVersionFromProjectLocationSecretSecretVersionName(projectLocationSecretSecretVersionName) {
        return this.pathTemplates.projectLocationSecretSecretVersionPathTemplate.match(projectLocationSecretSecretVersionName).secret_version;
    }
    /**
     * Return a fully-qualified projectSecret resource name string.
     *
     * @param {string} project
     * @param {string} secret
     * @returns {string} Resource name string.
     */
    projectSecretPath(project, secret) {
        return this.pathTemplates.projectSecretPathTemplate.render({
            project: project,
            secret: secret,
        });
    }
    /**
     * Parse the project from ProjectSecret resource.
     *
     * @param {string} projectSecretName
     *   A fully-qualified path representing project_secret resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectSecretName(projectSecretName) {
        return this.pathTemplates.projectSecretPathTemplate.match(projectSecretName)
            .project;
    }
    /**
     * Parse the secret from ProjectSecret resource.
     *
     * @param {string} projectSecretName
     *   A fully-qualified path representing project_secret resource.
     * @returns {string} A string representing the secret.
     */
    matchSecretFromProjectSecretName(projectSecretName) {
        return this.pathTemplates.projectSecretPathTemplate.match(projectSecretName)
            .secret;
    }
    /**
     * Return a fully-qualified projectSecretSecretVersion resource name string.
     *
     * @param {string} project
     * @param {string} secret
     * @param {string} secret_version
     * @returns {string} Resource name string.
     */
    projectSecretSecretVersionPath(project, secret, secretVersion) {
        return this.pathTemplates.projectSecretSecretVersionPathTemplate.render({
            project: project,
            secret: secret,
            secret_version: secretVersion,
        });
    }
    /**
     * Parse the project from ProjectSecretSecretVersion resource.
     *
     * @param {string} projectSecretSecretVersionName
     *   A fully-qualified path representing project_secret_secret_version resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromProjectSecretSecretVersionName(projectSecretSecretVersionName) {
        return this.pathTemplates.projectSecretSecretVersionPathTemplate.match(projectSecretSecretVersionName).project;
    }
    /**
     * Parse the secret from ProjectSecretSecretVersion resource.
     *
     * @param {string} projectSecretSecretVersionName
     *   A fully-qualified path representing project_secret_secret_version resource.
     * @returns {string} A string representing the secret.
     */
    matchSecretFromProjectSecretSecretVersionName(projectSecretSecretVersionName) {
        return this.pathTemplates.projectSecretSecretVersionPathTemplate.match(projectSecretSecretVersionName).secret;
    }
    /**
     * Parse the secret_version from ProjectSecretSecretVersion resource.
     *
     * @param {string} projectSecretSecretVersionName
     *   A fully-qualified path representing project_secret_secret_version resource.
     * @returns {string} A string representing the secret_version.
     */
    matchSecretVersionFromProjectSecretSecretVersionName(projectSecretSecretVersionName) {
        return this.pathTemplates.projectSecretSecretVersionPathTemplate.match(projectSecretSecretVersionName).secret_version;
    }
    /**
     * Return a fully-qualified topic resource name string.
     *
     * @param {string} project
     * @param {string} topic
     * @returns {string} Resource name string.
     */
    topicPath(project, topic) {
        return this.pathTemplates.topicPathTemplate.render({
            project: project,
            topic: topic,
        });
    }
    /**
     * Parse the project from Topic resource.
     *
     * @param {string} topicName
     *   A fully-qualified path representing Topic resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromTopicName(topicName) {
        return this.pathTemplates.topicPathTemplate.match(topicName).project;
    }
    /**
     * Parse the topic from Topic resource.
     *
     * @param {string} topicName
     *   A fully-qualified path representing Topic resource.
     * @returns {string} A string representing the topic.
     */
    matchTopicFromTopicName(topicName) {
        return this.pathTemplates.topicPathTemplate.match(topicName).topic;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.secretManagerServiceStub && !this._terminated) {
            return this.secretManagerServiceStub.then(stub => {
                this._terminated = true;
                stub.close();
                this.locationsClient.close();
            });
        }
        return Promise.resolve();
    }
    /**
     * Return a fully-qualified project resource name string.
     *
     * @param {string} project
     * @returns {string} Resource name string.
     */
    secretPath(project, secret) {
        return this.pathTemplates.secretPathTemplate.render({
            project: project,
            secret: secret,
        });
    }
    /**
     * Parse the project from Secret resource.
     *
     * @param {string} secretName
     *   A fully-qualified path representing Secret resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSecretName(secretName) {
        return this.pathTemplates.secretPathTemplate.match(secretName).project;
    }
    /**
     * Parse the secret from Secret resource.
     *
     * @param {string} secretName
     *   A fully-qualified path representing Secret resource.
     * @returns {string} A string representing the secret.
     */
    matchSecretFromSecretName(secretName) {
        return this.pathTemplates.secretPathTemplate.match(secretName).secret;
    }
    /**
     * Return a fully-qualified secretVersion resource name string.
     *
     * @param {string} project
     * @param {string} secret
     * @param {string} secret_version
     * @returns {string} Resource name string.
     */
    secretVersionPath(project, secret, secretVersion) {
        return this.pathTemplates.secretVersionPathTemplate.render({
            project: project,
            secret: secret,
            secret_version: secretVersion,
        });
    }
    /**
     * Parse the project from SecretVersion resource.
     *
     * @param {string} secretVersionName
     *   A fully-qualified path representing SecretVersion resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromSecretVersionName(secretVersionName) {
        return this.pathTemplates.secretVersionPathTemplate.match(secretVersionName)
            .project;
    }
    /**
     * Parse the secret from SecretVersion resource.
     *
     * @param {string} secretVersionName
     *   A fully-qualified path representing SecretVersion resource.
     * @returns {string} A string representing the secret.
     */
    matchSecretFromSecretVersionName(secretVersionName) {
        return this.pathTemplates.secretVersionPathTemplate.match(secretVersionName)
            .secret;
    }
    /**
     * Parse the secret_version from SecretVersion resource.
     *
     * @param {string} secretVersionName
     *   A fully-qualified path representing SecretVersion resource.
     * @returns {string} A string representing the secret_version.
     */
    matchSecretVersionFromSecretVersionName(secretVersionName) {
        return this.pathTemplates.secretVersionPathTemplate.match(secretVersionName)
            .secret_version;
    }
}
exports.SecretManagerServiceClient = SecretManagerServiceClient;
//# sourceMappingURL=secret_manager_service_client.js.map