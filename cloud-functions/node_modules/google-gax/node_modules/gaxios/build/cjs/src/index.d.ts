import { GaxiosOptions } from './common.js';
import { Gaxios } from './gaxios.js';
export { GaxiosError, GaxiosPromise, GaxiosResponse, GaxiosOptionsPrepared, RetryConfig, } from './common.js';
export { Gaxios, GaxiosOptions };
export * from './interceptor.js';
/**
 * The default instance used when the `request` method is directly
 * invoked.
 */
export declare const instance: Gaxios;
/**
 * Make an HTTP request using the given options.
 * @param opts Options for the request
 */
export declare function request<T>(opts: GaxiosOptions): Promise<import("./common.js").GaxiosResponse<T>>;
