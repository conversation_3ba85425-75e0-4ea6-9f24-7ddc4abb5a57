/*!
 * @js-sdsl/ordered-map v4.4.2
 * https://github.com/js-sdsl/js-sdsl
 * (c) 2021-present ZLY201 <<EMAIL>>
 * MIT license
 */
!function(t,i){"object"==typeof exports&&"undefined"!=typeof module?i(exports):"function"==typeof define&&define.amd?define(["exports"],i):i((t="undefined"!=typeof globalThis?globalThis:t||self).sdsl={})}(this,function(t){"use strict";var r=function(t,i){return(r=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,i){t.__proto__=i}:function(t,i){for(var e in i)Object.prototype.hasOwnProperty.call(i,e)&&(t[e]=i[e])}))(t,i)};function i(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function e(){this.constructor=t}r(t,i),t.prototype=null===i?Object.create(i):(e.prototype=i.prototype,new e)}function o(r,n){var o,s,h,u={label:0,sent:function(){if(1&h[0])throw h[1];return h[1]},trys:[],ops:[]},f={next:t(0),throw:t(1),return:t(2)};return"function"==typeof Symbol&&(f[Symbol.iterator]=function(){return this}),f;function t(e){return function(t){var i=[e,t];if(o)throw new TypeError("Generator is already executing.");for(;u=f&&i[f=0]?0:u;)try{if(o=1,s&&(h=2&i[0]?s.return:i[0]?s.throw||((h=s.return)&&h.call(s),0):s.next)&&!(h=h.call(s,i[1])).done)return h;switch(s=0,(i=h?[2&i[0],h.value]:i)[0]){case 0:case 1:h=i;break;case 4:return u.label++,{value:i[1],done:!1};case 5:u.label++,s=i[1],i=[0];continue;case 7:i=u.ops.pop(),u.trys.pop();continue;default:if(!(h=0<(h=u.trys).length&&h[h.length-1])&&(6===i[0]||2===i[0])){u=0;continue}if(3===i[0]&&(!h||i[1]>h[0]&&i[1]<h[3]))u.label=i[1];else if(6===i[0]&&u.label<h[1])u.label=h[1],h=i;else{if(!(h&&u.label<h[2])){h[2]&&u.ops.pop(),u.trys.pop();continue}u.label=h[2],u.ops.push(i)}}i=n.call(r,u)}catch(t){i=[6,t],s=0}finally{o=h=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}}}e.prototype.v=function(){var t=this,i=t.u.u===t;if(i&&1===t.l)t=t.i;else if(t.t)for(t=t.t;t.i;)t=t.i;else{if(i)return t.u;for(var e=t.u;e.t===t;)e=(t=e).u;t=e}return t},e.prototype.p=function(){var t=this;if(t.i){for(t=t.i;t.t;)t=t.t;return t}for(var i=t.u;i.i===t;)i=(t=i).u;return t.i!==i?i:t},e.prototype.T=function(){var t=this.u,i=this.i,e=i.t;return t.u===this?t.u=i:t.t===this?t.t=i:t.i=i,i.u=t,(i.t=this).u=i,(this.i=e)&&(e.u=this),i},e.prototype.O=function(){var t=this.u,i=this.t,e=i.i;return t.u===this?t.u=i:t.t===this?t.t=i:t.i=i,i.u=t,(i.i=this).u=i,(this.t=e)&&(e.u=this),i};var n=e;function e(t,i,e){void 0===e&&(e=1),this.t=void 0,this.i=void 0,this.u=void 0,this.h=t,this.o=i,this.l=e}i(u,s=n),u.prototype.T=function(){var t=s.prototype.T.call(this);return this.I(),t.I(),t},u.prototype.O=function(){var t=s.prototype.O.call(this);return this.I(),t.I(),t},u.prototype.I=function(){this._=1,this.t&&(this._+=this.t._),this.i&&(this._+=this.i._)};var s,h=u;function u(){var t=null!==s&&s.apply(this,arguments)||this;return t._=1,t}p.prototype.equals=function(t){return this.M===t.M};var f=p;function p(t){this.iteratorType=t=void 0===t?0:t}function l(){this.C=0}Object.defineProperty(l.prototype,"length",{get:function(){return this.C},enumerable:!1,configurable:!0}),l.prototype.size=function(){return this.C},l.prototype.empty=function(){return 0===this.C};i(y,c=l);var c,a=y;function y(){return null!==c&&c.apply(this,arguments)||this}function v(){throw new RangeError("Iterator access denied!")}i(A,d=a),A.prototype.j=function(t,i){for(var e=this.A;t;){var r=this.N(t.h,i);if(r<0)t=t.i;else{if(!(0<r))return t;t=(e=t).t}}return e},A.prototype.S=function(t,i){for(var e=this.A;t;)t=this.N(t.h,i)<=0?t.i:(e=t).t;return e},A.prototype.k=function(t,i){for(var e=this.A;t;){var r=this.N(t.h,i);if(r<0)t=(e=t).i;else{if(!(0<r))return t;t=t.t}}return e},A.prototype.B=function(t,i){for(var e=this.A;t;)t=this.N(t.h,i)<0?(e=t).i:t.t;return e},A.prototype.R=function(t){for(;;){var i,e=t.u;if(e===this.A)return;if(1===t.l)return void(t.l=0);if(t===e.t)if(1===(i=e.i).l)i.l=0,e.l=1,e===this.g?this.g=e.T():e.T();else{if(i.i&&1===i.i.l)return i.l=e.l,e.l=0,i.i.l=0,void(e===this.g?this.g=e.T():e.T());i.t&&1===i.t.l?(i.l=1,i.t.l=0,i.O()):(i.l=1,t=e)}else if(1===(i=e.t).l)i.l=0,e.l=1,e===this.g?this.g=e.O():e.O();else{if(i.t&&1===i.t.l)return i.l=e.l,e.l=0,i.t.l=0,void(e===this.g?this.g=e.O():e.O());i.i&&1===i.i.l?(i.l=1,i.i.l=0,i.T()):(i.l=1,t=e)}}},A.prototype.G=function(t){if(1===this.C)this.clear();else{for(var i=t;i.t||i.i;){if(i.i)for(i=i.i;i.t;)i=i.t;else i=i.t;var e=t.h,e=(t.h=i.h,i.h=e,t.o);t.o=i.o,i.o=e,t=i}this.A.t===i?this.A.t=i.u:this.A.i===i&&(this.A.i=i.u),this.R(i);var r=i.u;if(i===r.t?r.t=void 0:r.i=void 0,--this.C,this.g.l=0,this.enableIndex)for(;r!==this.A;)--r._,r=r.u}},A.prototype.P=function(t){for(var i="number"==typeof t?t:void 0,e="function"==typeof t?t:void 0,r=void 0===t?[]:void 0,n=0,o=this.g,s=[];s.length||o;)if(o)s.push(o),o=o.t;else{if(o=s.pop(),n===i)return o;r&&r.push(o),e&&e(o,n,this),n+=1,o=o.i}return r},A.prototype.q=function(t){for(;;){var i=t.u;if(0===i.l)return;var e,r,n=i.u;if(i===n.t){if((e=n.i)&&1===e.l){if(e.l=i.l=0,n===this.g)return;n.l=1,t=n;continue}if(t!==i.i)return i.l=0,n===this.g?this.g=n.O():n.O(),void(n.l=1);t.l=0,t.t&&(t.t.u=i),t.i&&(t.i.u=n),i.i=t.t,n.t=t.i,t.t=i,(t.i=n)===this.g?(this.g=t,this.A.u=t):(r=n.u).t===n?r.t=t:r.i=t}else{if((e=n.t)&&1===e.l){if(e.l=i.l=0,n===this.g)return;n.l=1,t=n;continue}if(t!==i.t)return i.l=0,n===this.g?this.g=n.T():n.T(),void(n.l=1);t.l=0,t.t&&(t.t.u=n),t.i&&(t.i.u=i),n.i=t.t,i.t=t.i,t.t=n,t.i=i,n===this.g?(this.g=t,this.A.u=t):(r=n.u).t===n?r.t=t:r.i=t}return t.u=n.u,i.u=t,n.u=t,n.l=1,void(this.enableIndex&&(i.I(),n.I(),t.I()))}},A.prototype.D=function(t,i,e){if(void 0===this.g)this.C+=1,this.g=new this.m(t,i,0),this.g.u=this.A,this.A.u=this.A.t=this.A.i=this.g;else{var r,n=this.A.t,o=this.N(n.h,t);if(0===o)n.o=i;else{if(0<o)n.t=new this.m(t,i),r=(n.t.u=n).t,this.A.t=r;else{o=this.A.i,n=this.N(o.h,t);if(0===n)return o.o=i,this.C;if(n<0)o.i=new this.m(t,i),r=(o.i.u=o).i,this.A.i=r;else{if(void 0!==e){n=e.M;if(n!==this.A){o=this.N(n.h,t);if(0===o)return n.o=i,this.C;if(0<o){e=n.v(),o=this.N(e.h,t);if(0===o)return e.o=i,this.C;o<0&&(r=new this.m(t,i),void 0===e.i?(e.i=r).u=e:(n.t=r).u=n)}}}if(void 0===r)for(r=this.g;;){var s=this.N(r.h,t);if(0<s){if(void 0===r.t){r.t=new this.m(t,i),r=(r.t.u=r).t;break}r=r.t}else{if(!(s<0))return r.o=i,this.C;if(void 0===r.i){r.i=new this.m(t,i),r=(r.i.u=r).i;break}r=r.i}}}}if(this.enableIndex)for(var h=r.u;h!==this.A;)h._+=1,h=h.u;this.q(r),this.C+=1}}return this.C},A.prototype.F=function(t,i){for(;t;){var e=this.N(t.h,i);if(e<0)t=t.i;else{if(!(0<e))return t;t=t.t}}return t||this.A},A.prototype.clear=function(){this.C=0,this.g=void 0,this.A.u=void 0,this.A.t=this.A.i=void 0},A.prototype.updateKeyByIterator=function(t,i){t=t.M;if(t===this.A&&v(),1!==this.C){var e=t.p().h;if(t===this.A.t)return 0<this.N(e,i)&&(t.h=i,!0);var r=t.v().h;if(t===this.A.i)return this.N(r,i)<0&&(t.h=i,!0);if(0<=this.N(r,i)||this.N(e,i)<=0)return!1}return t.h=i,!0},A.prototype.eraseElementByPos=function(t){if(t<0||t>this.C-1)throw new RangeError;t=this.P(t);return this.G(t),this.C},A.prototype.eraseElementByKey=function(t){return 0!==this.C&&(t=this.F(this.g,t))!==this.A&&(this.G(t),!0)},A.prototype.eraseElementByIterator=function(t){var i=t.M,e=(i===this.A&&v(),void 0===i.i);return 0===t.iteratorType?e&&t.next():e&&void 0!==i.t||t.next(),this.G(i),t},A.prototype.getHeight=function(){return 0===this.C?0:function t(i){return i?Math.max(t(i.t),t(i.i))+1:0}(this.g)};var d,a=A;function A(t,i){void 0===t&&(t=function(t,i){return t<i?-1:i<t?1:0}),void 0===i&&(i=!1);var e=d.call(this)||this;return e.g=void 0,e.N=t,e.enableIndex=i,e.m=i?h:n,e.A=new e.m,e}i(b,g=f),Object.defineProperty(b.prototype,"index",{get:function(){var t=this.M,i=this.A.u;if(t===this.A)return i?i._-1:0;var e=0;for(t.t&&(e+=t.t._);t!==i;){var r=t.u;t===r.i&&(e+=1,r.t)&&(e+=r.t._),t=r}return e},enumerable:!1,configurable:!0}),b.prototype.isAccessible=function(){return this.M!==this.A};var g,f=b;function b(t,i,e){e=g.call(this,e)||this;return e.M=t,e.A=i,0===e.iteratorType?(e.pre=function(){return this.M===this.A.t&&v(),this.M=this.M.v(),this},e.next=function(){return this.M===this.A&&v(),this.M=this.M.p(),this}):(e.pre=function(){return this.M===this.A.i&&v(),this.M=this.M.p(),this},e.next=function(){return this.M===this.A&&v(),this.M=this.M.v(),this}),e}i(m,w=f),Object.defineProperty(m.prototype,"pointer",{get:function(){this.M===this.A&&v();var r=this;return new Proxy([],{get:function(t,i){return"0"===i?r.M.h:"1"===i?r.M.o:(t[0]=r.M.h,t[1]=r.M.o,t[i])},set:function(t,i,e){if("1"!==i)throw new TypeError("prop must be 1");return r.M.o=e,!0}})},enumerable:!1,configurable:!0}),m.prototype.copy=function(){return new m(this.M,this.A,this.container,this.iteratorType)};var w,M=m;function m(t,i,e,r){t=w.call(this,t,i,r)||this;return t.container=e,t}i(O,C=a),O.prototype.begin=function(){return new M(this.A.t||this.A,this.A,this)},O.prototype.end=function(){return new M(this.A,this.A,this)},O.prototype.rBegin=function(){return new M(this.A.i||this.A,this.A,this,1)},O.prototype.rEnd=function(){return new M(this.A,this.A,this,1)},O.prototype.front=function(){var t;if(0!==this.C)return[(t=this.A.t).h,t.o]},O.prototype.back=function(){var t;if(0!==this.C)return[(t=this.A.i).h,t.o]},O.prototype.lowerBound=function(t){t=this.j(this.g,t);return new M(t,this.A,this)},O.prototype.upperBound=function(t){t=this.S(this.g,t);return new M(t,this.A,this)},O.prototype.reverseLowerBound=function(t){t=this.k(this.g,t);return new M(t,this.A,this)},O.prototype.reverseUpperBound=function(t){t=this.B(this.g,t);return new M(t,this.A,this)},O.prototype.forEach=function(r){this.P(function(t,i,e){r([t.h,t.o],i,e)})},O.prototype.setElement=function(t,i,e){return this.D(t,i,e)},O.prototype.getElementByPos=function(t){if(t<0||t>this.C-1)throw new RangeError;t=this.P(t);return[t.h,t.o]},O.prototype.find=function(t){t=this.F(this.g,t);return new M(t,this.A,this)},O.prototype.getElementByKey=function(t){return this.F(this.g,t).o},O.prototype.union=function(t){var i=this;return t.forEach(function(t){i.setElement(t[0],t[1])}),this.C},O.prototype[Symbol.iterator]=function(){var i,e,r,n;return o(this,function(t){switch(t.label){case 0:i=this.C,e=this.P(),r=0,t.label=1;case 1:return r<i?[4,[(n=e[r]).h,n.o]]:[3,4];case 2:t.sent(),t.label=3;case 3:return++r,[3,1];case 4:return[2]}})};var C,f=O;function O(t,i,e){void 0===t&&(t=[]);var i=C.call(this,i,e)||this,r=i;return t.forEach(function(t){r.setElement(t[0],t[1])}),i}t.OrderedMap=f,Object.defineProperty(t,"H",{value:!0})});
//# sourceMappingURL=ordered-map.min.js.map
