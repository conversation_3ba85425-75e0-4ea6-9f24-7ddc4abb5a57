/**
 * Cloud Function: onTenantCreate
 * 
 * Automatically provisions MCP API keys when a new tenant is created.
 * 
 * Trigger: Firestore document creation on /tenants/{tenantId}
 * Condition: doc.status === 'pending_key'
 * 
 * Actions:
 * 1. Create API key via Google API Keys service
 * 2. Store API key in Secret Manager
 * 3. Grant AiLex service account access to the secret
 * 4. Update tenant document with secret path and status
 */

import { onDocumentCreated } from 'firebase-functions/v2/firestore';
import { logger } from 'firebase-functions';
import { initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { ApiKeysClient } from '@google-cloud/apikeys';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { GoogleAuth } from 'google-auth-library';

// Initialize Firebase Admin
initializeApp();
const db = getFirestore();

// Cross-project configuration
const TENANT_PROJECT = process.env.TENANT_PROJECT || 'new-texas-laws';
const MCP_PROJECT = process.env.MCP_PROJECT || 'texas-laws-personalinjury';
const MCP_KEY_PROVISIONER_SA = process.env.MCP_KEY_PROVISIONER_SA || `mcp-key-provisioner@${MCP_PROJECT}.iam.gserviceaccount.com`;

// Service accounts that need access to secrets (in MCP project)
const AILEX_SERVICE_ACCOUNT = `main-jp@${MCP_PROJECT}.iam.gserviceaccount.com`;
const MCP_CLIENT_SERVICE_ACCOUNT = `mcp-client@${MCP_PROJECT}.iam.gserviceaccount.com`;

// Initialize Google Auth for impersonation
const auth = new GoogleAuth();

interface TenantDocument {
  id: string;
  name: string;
  status: 'pending_key' | 'active' | 'inactive';
  mcpSecretPath?: string;
  created_at: FirebaseFirestore.Timestamp;
  [key: string]: any;
}

/**
 * Get Google Cloud clients with impersonated credentials
 */
async function getImpersonatedClients() {
  try {
    logger.info(`Setting up impersonated clients`, {
      targetSA: MCP_KEY_PROVISIONER_SA,
      mcpProject: MCP_PROJECT,
    });

    // Create clients with explicit project and impersonation
    const apiKeysClient = new ApiKeysClient({
      projectId: MCP_PROJECT,
      authClient: new GoogleAuth({
        scopes: ['https://www.googleapis.com/auth/cloud-platform'],
        // Use the environment variable to specify impersonation
        credentials: process.env.GOOGLE_APPLICATION_CREDENTIALS ? undefined : {
          type: 'service_account',
          // This will be handled by the IAM impersonation we set up in Terraform
        },
      }),
    });

    const secretManagerClient = new SecretManagerServiceClient({
      projectId: MCP_PROJECT,
      authClient: new GoogleAuth({
        scopes: ['https://www.googleapis.com/auth/cloud-platform'],
      }),
    });

    // Test the impersonation by making a simple call
    try {
      await apiKeysClient.listKeys({
        parent: `projects/${MCP_PROJECT}/locations/global`,
        pageSize: 1,
      });
      logger.info(`Successfully verified impersonated access to API Keys service`);
    } catch (testError) {
      logger.warn(`Could not verify API Keys access, but continuing`, {
        error: testError instanceof Error ? testError.message : String(testError),
      });
    }

    logger.info(`Successfully created impersonated clients`, {
      targetSA: MCP_KEY_PROVISIONER_SA,
      mcpProject: MCP_PROJECT,
    });

    return { apiKeysClient, secretManagerClient };
  } catch (error) {
    logger.error(`Failed to create impersonated clients`, {
      error: error instanceof Error ? error.message : String(error),
      targetSA: MCP_KEY_PROVISIONER_SA,
      mcpProject: MCP_PROJECT,
    });
    throw error;
  }
}

/**
 * Main Cloud Function handler
 */
export const onTenantCreate = onDocumentCreated(
  {
    document: 'tenants/{tenantId}',
    region: 'us-central1',
    memory: '256MiB',
    timeoutSeconds: 30,
  },
  async (event) => {
    const tenantId = event.params.tenantId;
    const tenantData = event.data?.data() as TenantDocument;

    logger.info(`Processing tenant creation: ${tenantId}`, {
      tenantId,
      status: tenantData?.status,
    });

    // Only process tenants with 'pending_key' status
    if (!tenantData || tenantData.status !== 'pending_key') {
      logger.info(`Skipping tenant ${tenantId} - status: ${tenantData?.status}`);
      return;
    }

    try {
      // Get impersonated clients for cross-project operations
      logger.info(`Getting impersonated clients for cross-project access`, {
        tenantId,
        mcpProject: MCP_PROJECT,
        provisionerSA: MCP_KEY_PROVISIONER_SA,
      });
      const { apiKeysClient, secretManagerClient } = await getImpersonatedClients();

      // Step 1: Create API key in MCP project
      logger.info(`Creating API key for tenant: ${tenantId} in project: ${MCP_PROJECT}`);
      const apiKey = await createApiKey(tenantId, apiKeysClient);

      // Step 2: Store in Secret Manager in MCP project
      logger.info(`Storing API key in Secret Manager for tenant: ${tenantId} in project: ${MCP_PROJECT}`);
      const secretName = await storeApiKeyInSecretManager(tenantId, apiKey, secretManagerClient);

      // Step 3: Grant access to AiLex service accounts in MCP project
      logger.info(`Granting access to service accounts for tenant: ${tenantId} in project: ${MCP_PROJECT}`);
      await grantSecretAccess(secretName, secretManagerClient);

      // Step 4: Update tenant document in tenant project
      logger.info(`Updating tenant document for: ${tenantId} in project: ${TENANT_PROJECT}`);
      await updateTenantDocument(tenantId, secretName);

      logger.info(`Successfully provisioned MCP key for tenant: ${tenantId}`, {
        tenantId,
        secretName,
        mcpProject: MCP_PROJECT,
        tenantProject: TENANT_PROJECT,
      });

    } catch (error) {
      logger.error(`Failed to provision MCP key for tenant: ${tenantId}`, {
        tenantId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });

      // Update tenant status to indicate failure
      try {
        await db.collection('tenants').doc(tenantId).update({
          status: 'key_provisioning_failed',
          error: error instanceof Error ? error.message : String(error),
          updated_at: new Date(),
        });
      } catch (updateError) {
        logger.error(`Failed to update tenant status after error: ${tenantId}`, {
          tenantId,
          updateError: updateError instanceof Error ? updateError.message : String(updateError),
        });
      }

      throw error;
    }
  }
);

/**
 * Create API key for the tenant using Google API Keys service in MCP project
 */
async function createApiKey(tenantId: string, apiKeysClient: ApiKeysClient): Promise<string> {
  const keyName = `mcp-rules-api-${tenantId}`;
  const displayName = `MCP Rules Engine API Key - ${tenantId}`;

  const [operation] = await apiKeysClient.createKey({
    parent: `projects/${MCP_PROJECT}/locations/global`,
    keyId: keyName,
    key: {
      displayName,
      restrictions: {
        // Add API restrictions if needed
        // For now, we'll create keys without specific API restrictions
      },
    },
  });

  // Wait for the operation to complete
  const [key] = await operation.promise();

  if (!key.keyString) {
    throw new Error(`Failed to create API key for tenant: ${tenantId} in project: ${MCP_PROJECT}`);
  }

  logger.info(`Created API key for tenant: ${tenantId} in project: ${MCP_PROJECT}`, {
    tenantId,
    keyName: key.name,
    project: MCP_PROJECT,
  });

  return key.keyString;
}

/**
 * Store API key in Secret Manager in MCP project
 */
async function storeApiKeyInSecretManager(tenantId: string, apiKey: string, secretManagerClient: SecretManagerServiceClient): Promise<string> {
  const secretId = `mcp-key-${tenantId}`;
  const parent = `projects/${MCP_PROJECT}`;

  // Create the secret
  const [secret] = await secretManagerClient.createSecret({
    parent,
    secretId,
    secret: {
      labels: {
        tenant: tenantId,
        service: 'mcp-rules-engine',
        environment: process.env.NODE_ENV || 'production',
        managed_by: 'cloud-function',
        tenant_project: TENANT_PROJECT,
      },
      replication: {
        automatic: {},
      },
    },
  });

  // Add the secret version with the API key
  await secretManagerClient.addSecretVersion({
    parent: secret.name!,
    payload: {
      data: Buffer.from(apiKey, 'utf8'),
    },
  });

  logger.info(`Stored API key in Secret Manager for tenant: ${tenantId} in project: ${MCP_PROJECT}`, {
    tenantId,
    secretName: secret.name,
    project: MCP_PROJECT,
  });

  return secret.name!;
}

/**
 * Grant access to the secret for AiLex service accounts in MCP project
 */
async function grantSecretAccess(secretName: string, secretManagerClient: SecretManagerServiceClient): Promise<void> {
  const members = [
    `serviceAccount:${AILEX_SERVICE_ACCOUNT}`,
    `serviceAccount:${MCP_CLIENT_SERVICE_ACCOUNT}`,
  ];

  // Grant secretAccessor role to service accounts
  await secretManagerClient.setIamPolicy({
    resource: secretName,
    policy: {
      bindings: [
        {
          role: 'roles/secretmanager.secretAccessor',
          members,
        },
      ],
    },
  });

  logger.info(`Granted secret access to service accounts in project: ${MCP_PROJECT}`, {
    secretName,
    members,
    project: MCP_PROJECT,
  });
}

/**
 * Update tenant document with secret path and active status
 */
async function updateTenantDocument(tenantId: string, secretName: string): Promise<void> {
  await db.collection('tenants').doc(tenantId).update({
    mcpSecretPath: secretName,
    status: 'active',
    updated_at: new Date(),
  });

  logger.info(`Updated tenant document`, {
    tenantId,
    secretName,
    status: 'active',
  });
}
