/**
 * Cloud Function: onTenantCreate
 * 
 * Automatically provisions MCP API keys when a new tenant is created.
 * 
 * Trigger: Firestore document creation on /tenants/{tenantId}
 * Condition: doc.status === 'pending_key'
 * 
 * Actions:
 * 1. Create API key via Google API Keys service
 * 2. Store API key in Secret Manager
 * 3. Grant AiLex service account access to the secret
 * 4. Update tenant document with secret path and status
 */

import { onDocumentCreated } from 'firebase-functions/v2/firestore';
import { logger } from 'firebase-functions';
import { initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { ApiKeysClient } from '@google-cloud/apikeys';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';

// Initialize Firebase Admin
initializeApp();
const db = getFirestore();

// Initialize Google Cloud clients
const apiKeysClient = new ApiKeysClient();
const secretManagerClient = new SecretManagerServiceClient();

// Configuration
const PROJECT_ID = process.env.GOOGLE_CLOUD_PROJECT || 'new-texas-laws';
const AILEX_SERVICE_ACCOUNT = `main-jp@${PROJECT_ID}.iam.gserviceaccount.com`;
const MCP_CLIENT_SERVICE_ACCOUNT = `mcp-client@${PROJECT_ID}.iam.gserviceaccount.com`;

interface TenantDocument {
  id: string;
  name: string;
  status: 'pending_key' | 'active' | 'inactive';
  mcpSecretPath?: string;
  created_at: FirebaseFirestore.Timestamp;
  [key: string]: any;
}

/**
 * Main Cloud Function handler
 */
export const onTenantCreate = onDocumentCreated(
  {
    document: 'tenants/{tenantId}',
    region: 'us-central1',
    memory: '256MiB',
    timeoutSeconds: 30,
  },
  async (event) => {
    const tenantId = event.params.tenantId;
    const tenantData = event.data?.data() as TenantDocument;

    logger.info(`Processing tenant creation: ${tenantId}`, {
      tenantId,
      status: tenantData?.status,
    });

    // Only process tenants with 'pending_key' status
    if (!tenantData || tenantData.status !== 'pending_key') {
      logger.info(`Skipping tenant ${tenantId} - status: ${tenantData?.status}`);
      return;
    }

    try {
      // Step 1: Create API key
      logger.info(`Creating API key for tenant: ${tenantId}`);
      const apiKey = await createApiKey(tenantId);

      // Step 2: Store in Secret Manager
      logger.info(`Storing API key in Secret Manager for tenant: ${tenantId}`);
      const secretName = await storeApiKeyInSecretManager(tenantId, apiKey);

      // Step 3: Grant access to AiLex service accounts
      logger.info(`Granting access to service accounts for tenant: ${tenantId}`);
      await grantSecretAccess(secretName);

      // Step 4: Update tenant document
      logger.info(`Updating tenant document for: ${tenantId}`);
      await updateTenantDocument(tenantId, secretName);

      logger.info(`Successfully provisioned MCP key for tenant: ${tenantId}`, {
        tenantId,
        secretName,
      });

    } catch (error) {
      logger.error(`Failed to provision MCP key for tenant: ${tenantId}`, {
        tenantId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
      });

      // Update tenant status to indicate failure
      try {
        await db.collection('tenants').doc(tenantId).update({
          status: 'key_provisioning_failed',
          error: error instanceof Error ? error.message : String(error),
          updated_at: new Date(),
        });
      } catch (updateError) {
        logger.error(`Failed to update tenant status after error: ${tenantId}`, {
          tenantId,
          updateError: updateError instanceof Error ? updateError.message : String(updateError),
        });
      }

      throw error;
    }
  }
);

/**
 * Create API key for the tenant using Google API Keys service
 */
async function createApiKey(tenantId: string): Promise<string> {
  const keyName = `mcp-rules-api-${tenantId}`;
  const displayName = `MCP Rules Engine API Key - ${tenantId}`;

  const [operation] = await apiKeysClient.createKey({
    parent: `projects/${PROJECT_ID}/locations/global`,
    keyId: keyName,
    key: {
      displayName,
      restrictions: {
        // Add API restrictions if needed
        // For now, we'll create keys without specific API restrictions
      },
    },
  });

  // Wait for the operation to complete
  const [key] = await operation.promise();
  
  if (!key.keyString) {
    throw new Error(`Failed to create API key for tenant: ${tenantId}`);
  }

  logger.info(`Created API key for tenant: ${tenantId}`, {
    tenantId,
    keyName: key.name,
  });

  return key.keyString;
}

/**
 * Store API key in Secret Manager
 */
async function storeApiKeyInSecretManager(tenantId: string, apiKey: string): Promise<string> {
  const secretId = `mcp-key-${tenantId}`;
  const parent = `projects/${PROJECT_ID}`;

  // Create the secret
  const [secret] = await secretManagerClient.createSecret({
    parent,
    secretId,
    secret: {
      labels: {
        tenant: tenantId,
        service: 'mcp-rules-engine',
        environment: process.env.NODE_ENV || 'production',
        managed_by: 'cloud-function',
      },
      replication: {
        automatic: {},
      },
    },
  });

  // Add the secret version with the API key
  await secretManagerClient.addSecretVersion({
    parent: secret.name!,
    payload: {
      data: Buffer.from(apiKey, 'utf8'),
    },
  });

  logger.info(`Stored API key in Secret Manager for tenant: ${tenantId}`, {
    tenantId,
    secretName: secret.name,
  });

  return secret.name!;
}

/**
 * Grant access to the secret for AiLex service accounts
 */
async function grantSecretAccess(secretName: string): Promise<void> {
  const members = [
    `serviceAccount:${AILEX_SERVICE_ACCOUNT}`,
    `serviceAccount:${MCP_CLIENT_SERVICE_ACCOUNT}`,
  ];

  // Grant secretAccessor role to service accounts
  await secretManagerClient.setIamPolicy({
    resource: secretName,
    policy: {
      bindings: [
        {
          role: 'roles/secretmanager.secretAccessor',
          members,
        },
      ],
    },
  });

  logger.info(`Granted secret access to service accounts`, {
    secretName,
    members,
  });
}

/**
 * Update tenant document with secret path and active status
 */
async function updateTenantDocument(tenantId: string, secretName: string): Promise<void> {
  await db.collection('tenants').doc(tenantId).update({
    mcpSecretPath: secretName,
    status: 'active',
    updated_at: new Date(),
  });

  logger.info(`Updated tenant document`, {
    tenantId,
    secretName,
    status: 'active',
  });
}
