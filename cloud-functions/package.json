{"name": "tenant-onboarding-functions", "version": "1.0.0", "description": "Cloud Functions for automated tenant onboarding and MCP key provisioning", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "engines": {"node": "20"}, "dependencies": {"@google-cloud/apikeys": "^2.1.0", "@google-cloud/secret-manager": "^5.0.0", "firebase-admin": "^12.0.0", "firebase-functions": "^5.0.0", "google-auth-library": "^9.0.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "firebase-functions-test": "^3.1.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.0"}, "private": true}