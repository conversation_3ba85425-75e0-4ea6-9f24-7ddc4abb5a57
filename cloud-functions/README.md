# MCP Tenant Onboarding System

This directory contains the automated tenant onboarding system for the MCP (Model Context Protocol) Rules Engine. The system automatically provisions API keys and secrets when new tenants are created in Firestore.

## System Overview

When a new tenant document is created in the `tenants` collection in Firestore, this system automatically:

1. **Creates an API Key** in Google Cloud API Keys service
2. **Stores the API Key** securely in Google Secret Manager
3. **Updates the tenant document** with the secret path for easy retrieval

## Architecture

```
Firestore Document Creation
         ↓
   Cloud Function Trigger
         ↓
   Service Account Impersonation
         ↓
   API Key Creation + Secret Storage
         ↓
   Tenant Document Update
```

## Components

### 1. Cloud Function (`onTenantCreate.ts`)
- **Trigger**: Firestore document creation in `tenants/{tenantId}`
- **Runtime**: Node.js 20
- **Region**: us-central1
- **Service Account**: `<EMAIL>`

### 2. Service Accounts
- **Function SA**: `<EMAIL>`
  - Executes the Cloud Function
  - Has permission to impersonate the provisioner SA
- **Provisioner SA**: `<EMAIL>`
  - Creates API keys and secrets
  - Has minimal required permissions for key/secret management

### 3. Resources Created per Tenant
- **API Key**: `mcp-rules-api-{tenantId}`
- **Secret**: `mcp-key-{tenantId}`
- **Labels**: Applied for organization and management

## Environment Variables

The Cloud Function uses these environment variables:

- `TENANT_PROJECT`: `texas-laws-personalinjury` (where tenant data is stored)
- `MCP_PROJECT`: `texas-laws-personalinjury` (where API keys/secrets are created)
- `MCP_KEY_PROVISIONER_SA`: Service account for impersonation
- `NODE_ENV`: `production`

## Deployment

### Prerequisites
1. Google Cloud CLI installed and authenticated
2. Node.js 20+ installed
3. Required IAM permissions

### Deploy the Function
```bash
# Install dependencies
npm install

# Build TypeScript
npm run build

# Deploy to Google Cloud
gcloud functions deploy tenant-onboard \
  --gen2 \
  --runtime=nodejs20 \
  --region=us-central1 \
  --source=. \
  --entry-point=onTenantCreate \
  --trigger-event-filters="type=google.cloud.firestore.document.v1.created" \
  --trigger-event-filters="database=(default)" \
  --trigger-event-filters-path-pattern="document=tenants/{tenantId}" \
  --service-account=<EMAIL> \
  --set-env-vars="TENANT_PROJECT=texas-laws-personalinjury,MCP_PROJECT=texas-laws-personalinjury,MCP_KEY_PROVISIONER_SA=<EMAIL>,NODE_ENV=production" \
  --memory=256Mi \
  --timeout=30s
```

## Verification

### Check Function Status
```bash
gcloud functions list --filter="name:tenant-onboard"
```

### View Function Logs
```bash
gcloud functions logs read tenant-onboard --limit=10
```

### List Created API Keys
```bash
gcloud alpha services api-keys list
```

### List Created Secrets
```bash
gcloud secrets list
```

### Run System Verification
```bash
node verify-system.js
```

## Security Features

1. **Service Account Impersonation**: Function uses minimal permissions and impersonates a dedicated provisioner SA
2. **Secret Storage**: API keys are stored securely in Google Secret Manager
3. **Least Privilege**: Each service account has only the minimum required permissions
4. **Audit Trail**: All operations are logged for security monitoring

## Monitoring

- **Function Logs**: Available in Google Cloud Logging
- **Metrics**: Function execution metrics in Google Cloud Monitoring
- **Alerts**: Can be configured for function failures or errors

## Troubleshooting

### Common Issues

1. **Permission Denied**: Check service account permissions and impersonation setup
2. **Function Not Triggering**: Verify Firestore trigger configuration
3. **API Key Creation Failed**: Check provisioner SA permissions

### Debug Commands
```bash
# Check function configuration
gcloud functions describe tenant-onboard --region=us-central1

# View recent logs
gcloud functions logs read tenant-onboard --limit=20

# Test function manually (for debugging)
gcloud functions call tenant-onboard --data='{"test": true}'
```

## Files

- `onTenantCreate.ts` - Main Cloud Function code
- `index.ts` - Function export entry point
- `package.json` - Node.js dependencies
- `tsconfig.json` - TypeScript configuration
- `verify-system.js` - System verification script
- `README.md` - This documentation

## Status

✅ **System is operational and deployed**

The MCP tenant onboarding system is successfully deployed and functional. It automatically provisions API keys and secrets for new tenants created in the Firestore `tenants` collection.
