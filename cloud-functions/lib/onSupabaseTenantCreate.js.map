{"version": 3, "file": "onSupabaseTenantCreate.js", "sourceRoot": "", "sources": ["../onSupabaseTenantCreate.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;GAaG;;;AAEH,uDAAwD;AACxD,2DAA4C;AAC5C,mDAAsD;AACtD,iEAA0E;AAC1E,uDAAqD;AAErD,wBAAwB;AACxB,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,2BAA2B,CAAC;AAC3E,MAAM,sBAAsB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,uEAAuE,CAAC;AAC7I,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,0CAA0C,CAAC;AAC5F,MAAM,oBAAoB,GAAG,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC;AAC9D,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,qBAAqB,CAAC;AAE3E,uCAAuC;AACvC,MAAM,QAAQ,GAAG,IAAA,0BAAY,EAAC,YAAY,EAAE,oBAAqB,CAAC,CAAC;AAkBnE;;GAEG;AACU,QAAA,sBAAsB,GAAG,IAAA,iBAAS,EAC7C;IACE,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;IAClB,IAAI,EAAE,IAAI;CACX,EACD,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;;IACjB,wBAAwB;IACxB,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,qBAAqB,CAAW,CAAC;IAC/D,IAAI,CAAC,SAAS,IAAI,SAAS,KAAK,cAAc,EAAE,CAAC;QAC/C,2BAAM,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QACzC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,CAAC,CAAC;QAChD,OAAO;IACT,CAAC;IAED,4BAA4B;IAC5B,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;QAC1B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,oBAAoB,EAAE,CAAC,CAAC;QACtD,OAAO;IACT,CAAC;IAED,IAAI,CAAC;QACH,MAAM,OAAO,GAAmB,GAAG,CAAC,IAAI,CAAC;QAEzC,2BAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;YACtC,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,QAAQ,EAAE,MAAA,OAAO,CAAC,MAAM,0CAAE,SAAS;SACpC,CAAC,CAAC;QAEH,oDAAoD;QACpD,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,KAAK,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAC3F,2BAAM,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC;YACnD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;YACnD,OAAO;QACT,CAAC;QAED,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAE9B,iDAAiD;QACjD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,UAAU,KAAK,aAAa,EAAE,CAAC;YACnD,2BAAM,CAAC,IAAI,CAAC,mBAAmB,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,SAAS,cAAc,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,EAAE,CAAC,CAAC;YACpF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,+BAA+B,EAAE,CAAC,CAAC;YACnE,OAAO;QACT,CAAC;QAED,2BAAM,CAAC,IAAI,CAAC,+BAA+B,MAAM,CAAC,SAAS,EAAE,EAAE;YAC7D,QAAQ,EAAE,MAAM,CAAC,SAAS;YAC1B,QAAQ,EAAE,MAAM,CAAC,IAAI;YACrB,MAAM,EAAE,MAAM,CAAC,UAAU;SAC1B,CAAC,CAAC;QAEH,qBAAqB;QACrB,MAAM,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAEpC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,+BAA+B;YACxC,QAAQ,EAAE,MAAM,CAAC,SAAS;SAC3B,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,2BAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QACjD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;IAC3D,CAAC;AACH,CAAC,CACF,CAAC;AAEF;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAAC,MAAoB;IACvD,IAAI,CAAC;QACH,wDAAwD;QACxD,2BAAM,CAAC,IAAI,CAAC,uDAAuD,EAAE;YACnE,QAAQ,EAAE,MAAM,CAAC,SAAS;YAC1B,UAAU,EAAE,WAAW;YACvB,aAAa,EAAE,sBAAsB;SACtC,CAAC,CAAC;QACH,MAAM,EAAE,aAAa,EAAE,mBAAmB,EAAE,GAAG,MAAM,sBAAsB,EAAE,CAAC;QAE9E,wCAAwC;QACxC,2BAAM,CAAC,IAAI,CAAC,gCAAgC,MAAM,CAAC,SAAS,gBAAgB,WAAW,EAAE,CAAC,CAAC;QAC3F,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,MAAM,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAEnE,iDAAiD;QACjD,2BAAM,CAAC,IAAI,CAAC,iDAAiD,MAAM,CAAC,SAAS,gBAAgB,WAAW,EAAE,CAAC,CAAC;QAC5G,MAAM,UAAU,GAAG,MAAM,0BAA0B,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE,mBAAmB,CAAC,CAAC;QAEnG,gEAAgE;QAChE,2BAAM,CAAC,IAAI,CAAC,mDAAmD,MAAM,CAAC,SAAS,gBAAgB,WAAW,EAAE,CAAC,CAAC;QAC9G,MAAM,iBAAiB,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;QAEzD,oEAAoE;QACpE,2BAAM,CAAC,IAAI,CAAC,kDAAkD,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;QAClF,MAAM,kBAAkB,CAAC,MAAM,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QAEvD,2BAAM,CAAC,IAAI,CAAC,sDAAsD,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;IAExF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,2BAAM,CAAC,KAAK,CAAC,iDAAiD,MAAM,CAAC,SAAS,EAAE,EAAE,KAAK,CAAC,CAAC;QAEzF,iCAAiC;QACjC,MAAM,kBAAkB,CAAC,MAAM,CAAC,SAAS,EAAE,IAAI,EAAE,yBAAyB,CAAC,CAAC;QAE5E,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,sBAAsB;IACnC,qCAAqC;IACrC,6EAA6E;IAC7E,MAAM,aAAa,GAAG,IAAI,uBAAa,CAAC;QACtC,SAAS,EAAE,WAAW;KACvB,CAAC,CAAC;IAEH,MAAM,mBAAmB,GAAG,IAAI,2CAA0B,CAAC;QACzD,SAAS,EAAE,WAAW;KACvB,CAAC,CAAC;IAEH,OAAO,EAAE,aAAa,EAAE,mBAAmB,EAAE,CAAC;AAChD,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,YAAY,CAAC,QAAgB,EAAE,aAA4B;IACxE,MAAM,OAAO,GAAG,iBAAiB,QAAQ,EAAE,CAAC;IAC5C,MAAM,WAAW,GAAG,8BAA8B,QAAQ,EAAE,CAAC;IAE7D,MAAM,CAAC,SAAS,CAAC,GAAG,MAAM,aAAa,CAAC,SAAS,CAAC;QAChD,MAAM,EAAE,YAAY,WAAW,mBAAmB;QAClD,KAAK,EAAE,OAAO;QACd,GAAG,EAAE;YACH,WAAW;YACX,YAAY,EAAE;YACZ,iCAAiC;YACjC,+DAA+D;aAChE;SACF;KACF,CAAC,CAAC;IAEH,qCAAqC;IACrC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,SAAS,CAAC,OAAO,EAAE,CAAC;IAExC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC;QACnB,MAAM,IAAI,KAAK,CAAC,wCAAwC,QAAQ,gBAAgB,WAAW,EAAE,CAAC,CAAC;IACjG,CAAC;IAED,2BAAM,CAAC,IAAI,CAAC,+BAA+B,QAAQ,EAAE,EAAE;QACrD,OAAO,EAAE,GAAG,CAAC,IAAI;QACjB,WAAW,EAAE,GAAG,CAAC,WAAW;KAC7B,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC,SAAS,CAAC;AACvB,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,0BAA0B,CACvC,QAAgB,EAChB,MAAc,EACd,mBAA+C;IAE/C,MAAM,QAAQ,GAAG,WAAW,QAAQ,EAAE,CAAC;IACvC,MAAM,MAAM,GAAG,YAAY,WAAW,EAAE,CAAC;IAEzC,oBAAoB;IACpB,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,mBAAmB,CAAC,YAAY,CAAC;QACtD,MAAM;QACN,QAAQ;QACR,MAAM,EAAE;YACN,WAAW,EAAE;gBACX,SAAS,EAAE,EAAE;aACd;YACD,MAAM,EAAE;gBACN,MAAM,EAAE,QAAQ;gBAChB,OAAO,EAAE,kBAAkB;gBAC3B,WAAW,EAAE,SAAS;gBACtB,UAAU,EAAE,gBAAgB;aAC7B;SACF;KACF,CAAC,CAAC;IAEH,0CAA0C;IAC1C,MAAM,CAAC,OAAO,CAAC,GAAG,MAAM,mBAAmB,CAAC,gBAAgB,CAAC;QAC3D,MAAM,EAAE,MAAM,CAAC,IAAK;QACpB,OAAO,EAAE;YACP,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;SAClC;KACF,CAAC,CAAC;IAEH,2BAAM,CAAC,IAAI,CAAC,gDAAgD,QAAQ,EAAE,EAAE;QACtE,UAAU,EAAE,MAAM,CAAC,IAAI;QACvB,WAAW,EAAE,OAAO,CAAC,IAAI;KAC1B,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC,IAAK,CAAC;AACtB,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,iBAAiB,CAC9B,UAAkB,EAClB,mBAA+C;IAE/C,kDAAkD;IAClD,MAAM,eAAe,GAAG;QACtB,0CAA0C,WAAW,0BAA0B;QAC/E,8CAA8C;KAC/C,CAAC;IAEF,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;QACrC,MAAM,mBAAmB,CAAC,YAAY,CAAC;YACrC,QAAQ,EAAE,UAAU;YACpB,MAAM,EAAE;gBACN,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,oCAAoC;wBAC1C,OAAO,EAAE,CAAC,MAAM,CAAC;qBAClB;iBACF;aACF;SACF,CAAC,CAAC;QAEH,2BAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,EAAE,EAAE;YACjD,UAAU;YACV,MAAM;SACP,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,kBAAkB,CAC/B,QAAgB,EAChB,UAAyB,EACzB,SAA+C,QAAQ;IAEvD,MAAM,UAAU,GAA0B;QACxC,UAAU,EAAE,MAAM;KACnB,CAAC;IAEF,IAAI,UAAU,EAAE,CAAC;QACf,UAAU,CAAC,eAAe,GAAG,UAAU,CAAC;IAC1C,CAAC;IAED,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;SAC7B,MAAM,CAAC,SAAS,CAAC;SACjB,IAAI,CAAC,OAAO,CAAC;SACb,MAAM,CAAC,UAAU,CAAC;SAClB,EAAE,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IAE7B,IAAI,KAAK,EAAE,CAAC;QACV,MAAM,IAAI,KAAK,CAAC,mCAAmC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IACtE,CAAC;IAED,2BAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;QAC/C,QAAQ;QACR,MAAM;QACN,UAAU;KACX,CAAC,CAAC;AACL,CAAC"}