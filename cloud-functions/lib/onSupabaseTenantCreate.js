"use strict";
/**
 * Cloud Function: onSupabaseTenantCreate
 *
 * Automatically provisions MCP API keys when a new tenant is created in Supabase.
 *
 * Trigger: HTTP webhook from Supabase database trigger
 * Condition: mcp_status === 'pending_key'
 *
 * Actions:
 * 1. Create API key via Google API Keys service
 * 2. Store API key in Secret Manager
 * 3. Grant AiLex service account access to the secret
 * 4. Update Supabase tenant record with secret path and status
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.onSupabaseTenantCreate = void 0;
const https_1 = require("firebase-functions/v2/https");
const firebase_functions_1 = require("firebase-functions");
const apikeys_1 = require("@google-cloud/apikeys");
const secret_manager_1 = require("@google-cloud/secret-manager");
const supabase_js_1 = require("@supabase/supabase-js");
// Environment variables
const MCP_PROJECT = process.env.MCP_PROJECT || 'texas-laws-personalinjury';
const MCP_KEY_PROVISIONER_SA = process.env.MCP_KEY_PROVISIONER_SA || '<EMAIL>';
const SUPABASE_URL = process.env.SUPABASE_URL || 'https://anwefmklplkjxkmzpnva.supabase.co';
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY;
const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET || 'your-webhook-secret';
// Supabase client for database updates
const supabase = (0, supabase_js_1.createClient)(SUPABASE_URL, SUPABASE_SERVICE_KEY);
/**
 * Main Cloud Function handler for Supabase webhooks
 */
exports.onSupabaseTenantCreate = (0, https_1.onRequest)({
    region: 'us-central1',
    memory: '256MiB',
    timeoutSeconds: 30,
    cors: true,
}, async (req, res) => {
    var _a;
    // Verify webhook secret
    const signature = req.headers['x-webhook-signature'];
    if (!signature || signature !== WEBHOOK_SECRET) {
        firebase_functions_1.logger.warn('Invalid webhook signature');
        res.status(401).json({ error: 'Unauthorized' });
        return;
    }
    // Only handle POST requests
    if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
    }
    try {
        const payload = req.body;
        firebase_functions_1.logger.info('Received webhook payload', {
            type: payload.type,
            table: payload.table,
            schema: payload.schema,
            tenantId: (_a = payload.record) === null || _a === void 0 ? void 0 : _a.tenant_id,
        });
        // Only process INSERT events on tenants.firms table
        if (payload.type !== 'INSERT' || payload.table !== 'firms' || payload.schema !== 'tenants') {
            firebase_functions_1.logger.info('Skipping non-relevant webhook event');
            res.status(200).json({ message: 'Event ignored' });
            return;
        }
        const tenant = payload.record;
        // Only process tenants with 'pending_key' status
        if (!tenant || tenant.mcp_status !== 'pending_key') {
            firebase_functions_1.logger.info(`Skipping tenant ${tenant === null || tenant === void 0 ? void 0 : tenant.tenant_id} - status: ${tenant === null || tenant === void 0 ? void 0 : tenant.mcp_status}`);
            res.status(200).json({ message: 'Tenant status not pending_key' });
            return;
        }
        firebase_functions_1.logger.info(`Processing tenant creation: ${tenant.tenant_id}`, {
            tenantId: tenant.tenant_id,
            firmName: tenant.name,
            status: tenant.mcp_status,
        });
        // Process the tenant
        await processTenantCreation(tenant);
        res.status(200).json({
            message: 'Tenant processed successfully',
            tenantId: tenant.tenant_id
        });
    }
    catch (error) {
        firebase_functions_1.logger.error('Error processing webhook:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
/**
 * Process tenant creation by provisioning MCP resources
 */
async function processTenantCreation(tenant) {
    try {
        // Get impersonated clients for cross-project operations
        firebase_functions_1.logger.info(`Getting impersonated clients for cross-project access`, {
            tenantId: tenant.tenant_id,
            mcpProject: MCP_PROJECT,
            provisionerSA: MCP_KEY_PROVISIONER_SA,
        });
        const { apiKeysClient, secretManagerClient } = await getImpersonatedClients();
        // Step 1: Create API key in MCP project
        firebase_functions_1.logger.info(`Creating API key for tenant: ${tenant.tenant_id} in project: ${MCP_PROJECT}`);
        const apiKey = await createApiKey(tenant.tenant_id, apiKeysClient);
        // Step 2: Store in Secret Manager in MCP project
        firebase_functions_1.logger.info(`Storing API key in Secret Manager for tenant: ${tenant.tenant_id} in project: ${MCP_PROJECT}`);
        const secretName = await storeApiKeyInSecretManager(tenant.tenant_id, apiKey, secretManagerClient);
        // Step 3: Grant access to AiLex service accounts in MCP project
        firebase_functions_1.logger.info(`Granting access to service accounts for tenant: ${tenant.tenant_id} in project: ${MCP_PROJECT}`);
        await grantSecretAccess(secretName, secretManagerClient);
        // Step 4: Update Supabase tenant record with secret path and status
        firebase_functions_1.logger.info(`Updating tenant record in Supabase for tenant: ${tenant.tenant_id}`);
        await updateTenantRecord(tenant.tenant_id, secretName);
        firebase_functions_1.logger.info(`Successfully provisioned MCP resources for tenant: ${tenant.tenant_id}`);
    }
    catch (error) {
        firebase_functions_1.logger.error(`Failed to provision MCP resources for tenant: ${tenant.tenant_id}`, error);
        // Update tenant status to failed
        await updateTenantRecord(tenant.tenant_id, null, 'key_provisioning_failed');
        throw error;
    }
}
/**
 * Get Google Cloud clients for MCP project operations
 */
async function getImpersonatedClients() {
    // Create clients for the MCP project
    // The Cloud Function's service account should have the necessary permissions
    const apiKeysClient = new apikeys_1.ApiKeysClient({
        projectId: MCP_PROJECT,
    });
    const secretManagerClient = new secret_manager_1.SecretManagerServiceClient({
        projectId: MCP_PROJECT,
    });
    return { apiKeysClient, secretManagerClient };
}
/**
 * Create API key for the tenant using Google API Keys service in MCP project
 */
async function createApiKey(tenantId, apiKeysClient) {
    const keyName = `mcp-rules-api-${tenantId}`;
    const displayName = `MCP Rules Engine API Key - ${tenantId}`;
    const [operation] = await apiKeysClient.createKey({
        parent: `projects/${MCP_PROJECT}/locations/global`,
        keyId: keyName,
        key: {
            displayName,
            restrictions: {
            // Add API restrictions if needed
            // For now, we'll create keys without specific API restrictions
            },
        },
    });
    // Wait for the operation to complete
    const [key] = await operation.promise();
    if (!key.keyString) {
        throw new Error(`Failed to create API key for tenant: ${tenantId} in project: ${MCP_PROJECT}`);
    }
    firebase_functions_1.logger.info(`Created API key for tenant: ${tenantId}`, {
        keyName: key.name,
        displayName: key.displayName,
    });
    return key.keyString;
}
/**
 * Store API key in Secret Manager in MCP project
 */
async function storeApiKeyInSecretManager(tenantId, apiKey, secretManagerClient) {
    const secretId = `mcp-key-${tenantId}`;
    const parent = `projects/${MCP_PROJECT}`;
    // Create the secret
    const [secret] = await secretManagerClient.createSecret({
        parent,
        secretId,
        secret: {
            replication: {
                automatic: {},
            },
            labels: {
                tenant: tenantId,
                service: 'mcp-rules-engine',
                environment: 'default',
                managed_by: 'cloud-function',
            },
        },
    });
    // Add the secret version with the API key
    const [version] = await secretManagerClient.addSecretVersion({
        parent: secret.name,
        payload: {
            data: Buffer.from(apiKey, 'utf8'),
        },
    });
    firebase_functions_1.logger.info(`Stored API key in Secret Manager for tenant: ${tenantId}`, {
        secretName: secret.name,
        versionName: version.name,
    });
    return secret.name;
}
/**
 * Grant access to the secret for AiLex service accounts
 */
async function grantSecretAccess(secretName, secretManagerClient) {
    // Service accounts that need access to the secret
    const serviceAccounts = [
        `serviceAccount:tenant-onboard-function@${MCP_PROJECT}.iam.gserviceaccount.com`,
        // Add other service accounts that need access
    ];
    for (const member of serviceAccounts) {
        await secretManagerClient.setIamPolicy({
            resource: secretName,
            policy: {
                bindings: [
                    {
                        role: 'roles/secretmanager.secretAccessor',
                        members: [member],
                    },
                ],
            },
        });
        firebase_functions_1.logger.info(`Granted secret access to: ${member}`, {
            secretName,
            member,
        });
    }
}
/**
 * Update tenant record in Supabase with secret path and status
 */
async function updateTenantRecord(tenantId, secretPath, status = 'active') {
    const updateData = {
        mcp_status: status,
    };
    if (secretPath) {
        updateData.mcp_secret_path = secretPath;
    }
    const { error } = await supabase
        .schema('tenants')
        .from('firms')
        .update(updateData)
        .eq('tenant_id', tenantId);
    if (error) {
        throw new Error(`Failed to update tenant record: ${error.message}`);
    }
    firebase_functions_1.logger.info(`Updated tenant record in Supabase`, {
        tenantId,
        status,
        secretPath,
    });
}
//# sourceMappingURL=onSupabaseTenantCreate.js.map