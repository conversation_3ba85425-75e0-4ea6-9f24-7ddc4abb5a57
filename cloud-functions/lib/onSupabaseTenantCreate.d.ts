/**
 * Cloud Function: onSupabaseTenantCreate
 *
 * Automatically provisions MCP API keys when a new tenant is created in Supabase.
 *
 * Trigger: HTTP webhook from Supabase database trigger
 * Condition: mcp_status === 'pending_key'
 *
 * Actions:
 * 1. Create API key via Google API Keys service
 * 2. Store API key in Secret Manager
 * 3. Grant AiLex service account access to the secret
 * 4. Update Supabase tenant record with secret path and status
 */
/**
 * Main Cloud Function handler for Supabase webhooks
 */
export declare const onSupabaseTenantCreate: import("firebase-functions/v2/https").HttpsFunction;
//# sourceMappingURL=onSupabaseTenantCreate.d.ts.map