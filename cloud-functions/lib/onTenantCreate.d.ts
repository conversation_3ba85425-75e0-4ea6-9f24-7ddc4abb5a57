/**
 * Cloud Function: onTenantCreate
 *
 * Automatically provisions MCP API keys when a new tenant is created.
 *
 * Trigger: Firestore document creation on /tenants/{tenantId}
 * Condition: doc.status === 'pending_key'
 *
 * Actions:
 * 1. Create API key via Google API Keys service
 * 2. Store API key in Secret Manager
 * 3. Grant AiLex service account access to the secret
 * 4. Update tenant document with secret path and status
 */
/**
 * Main Cloud Function handler
 */
export declare const onTenantCreate: import("firebase-functions/v2/core").CloudFunction<import("firebase-functions/v2/firestore").FirestoreEvent<import("firebase-functions/v2/firestore").QueryDocumentSnapshot | undefined, {
    tenantId: string;
}>>;
//# sourceMappingURL=onTenantCreate.d.ts.map