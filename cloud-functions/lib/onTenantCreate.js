"use strict";
/**
 * Cloud Function: onTenantCreate
 *
 * Automatically provisions MCP API keys when a new tenant is created.
 *
 * Trigger: Firestore document creation on /tenants/{tenantId}
 * Condition: doc.status === 'pending_key'
 *
 * Actions:
 * 1. Create API key via Google API Keys service
 * 2. Store API key in Secret Manager
 * 3. Grant AiLex service account access to the secret
 * 4. Update tenant document with secret path and status
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.onTenantCreate = void 0;
const firestore_1 = require("firebase-functions/v2/firestore");
const firebase_functions_1 = require("firebase-functions");
const app_1 = require("firebase-admin/app");
const firestore_2 = require("firebase-admin/firestore");
const apikeys_1 = require("@google-cloud/apikeys");
const secret_manager_1 = require("@google-cloud/secret-manager");
// Removed unused GoogleAuth import
// Initialize Firebase Admin
(0, app_1.initializeApp)();
const db = (0, firestore_2.getFirestore)();
// Cross-project configuration
const TENANT_PROJECT = process.env.TENANT_PROJECT || 'new-texas-laws';
const MCP_PROJECT = process.env.MCP_PROJECT || 'texas-laws-personalinjury';
const MCP_KEY_PROVISIONER_SA = process.env.MCP_KEY_PROVISIONER_SA || `mcp-key-provisioner@${MCP_PROJECT}.iam.gserviceaccount.com`;
// Service accounts that need access to secrets (in MCP project)
const AILEX_SERVICE_ACCOUNT = `main-jp@${MCP_PROJECT}.iam.gserviceaccount.com`;
const MCP_CLIENT_SERVICE_ACCOUNT = `mcp-client@${MCP_PROJECT}.iam.gserviceaccount.com`;
/**
 * Get Google Cloud clients with impersonated credentials
 */
async function getImpersonatedClients() {
    try {
        firebase_functions_1.logger.info(`Setting up impersonated clients`, {
            targetSA: MCP_KEY_PROVISIONER_SA,
            mcpProject: MCP_PROJECT,
        });
        // Create clients with explicit project and impersonation
        const apiKeysClient = new apikeys_1.ApiKeysClient({
            projectId: MCP_PROJECT,
        });
        const secretManagerClient = new secret_manager_1.SecretManagerServiceClient({
            projectId: MCP_PROJECT,
        });
        // Test the impersonation by making a simple call
        try {
            await apiKeysClient.listKeys({
                parent: `projects/${MCP_PROJECT}/locations/global`,
                pageSize: 1,
            });
            firebase_functions_1.logger.info(`Successfully verified impersonated access to API Keys service`);
        }
        catch (testError) {
            firebase_functions_1.logger.warn(`Could not verify API Keys access, but continuing`, {
                error: testError instanceof Error ? testError.message : String(testError),
            });
        }
        firebase_functions_1.logger.info(`Successfully created impersonated clients`, {
            targetSA: MCP_KEY_PROVISIONER_SA,
            mcpProject: MCP_PROJECT,
        });
        return { apiKeysClient, secretManagerClient };
    }
    catch (error) {
        firebase_functions_1.logger.error(`Failed to create impersonated clients`, {
            error: error instanceof Error ? error.message : String(error),
            targetSA: MCP_KEY_PROVISIONER_SA,
            mcpProject: MCP_PROJECT,
        });
        throw error;
    }
}
/**
 * Main Cloud Function handler
 */
exports.onTenantCreate = (0, firestore_1.onDocumentCreated)({
    document: 'tenants/{tenantId}',
    region: 'us-central1',
    memory: '256MiB',
    timeoutSeconds: 30,
}, async (event) => {
    var _a;
    const tenantId = event.params.tenantId;
    const tenantData = (_a = event.data) === null || _a === void 0 ? void 0 : _a.data();
    firebase_functions_1.logger.info(`Processing tenant creation: ${tenantId}`, {
        tenantId,
        status: tenantData === null || tenantData === void 0 ? void 0 : tenantData.status,
    });
    // Only process tenants with 'pending_key' status
    if (!tenantData || tenantData.status !== 'pending_key') {
        firebase_functions_1.logger.info(`Skipping tenant ${tenantId} - status: ${tenantData === null || tenantData === void 0 ? void 0 : tenantData.status}`);
        return;
    }
    try {
        // Get impersonated clients for cross-project operations
        firebase_functions_1.logger.info(`Getting impersonated clients for cross-project access`, {
            tenantId,
            mcpProject: MCP_PROJECT,
            provisionerSA: MCP_KEY_PROVISIONER_SA,
        });
        const { apiKeysClient, secretManagerClient } = await getImpersonatedClients();
        // Step 1: Create API key in MCP project
        firebase_functions_1.logger.info(`Creating API key for tenant: ${tenantId} in project: ${MCP_PROJECT}`);
        const apiKey = await createApiKey(tenantId, apiKeysClient);
        // Step 2: Store in Secret Manager in MCP project
        firebase_functions_1.logger.info(`Storing API key in Secret Manager for tenant: ${tenantId} in project: ${MCP_PROJECT}`);
        const secretName = await storeApiKeyInSecretManager(tenantId, apiKey, secretManagerClient);
        // Step 3: Grant access to AiLex service accounts in MCP project
        firebase_functions_1.logger.info(`Granting access to service accounts for tenant: ${tenantId} in project: ${MCP_PROJECT}`);
        await grantSecretAccess(secretName, secretManagerClient);
        // Step 4: Update tenant document in tenant project
        firebase_functions_1.logger.info(`Updating tenant document for: ${tenantId} in project: ${TENANT_PROJECT}`);
        await updateTenantDocument(tenantId, secretName);
        firebase_functions_1.logger.info(`Successfully provisioned MCP key for tenant: ${tenantId}`, {
            tenantId,
            secretName,
            mcpProject: MCP_PROJECT,
            tenantProject: TENANT_PROJECT,
        });
    }
    catch (error) {
        firebase_functions_1.logger.error(`Failed to provision MCP key for tenant: ${tenantId}`, {
            tenantId,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined,
        });
        // Update tenant status to indicate failure
        try {
            await db.collection('tenants').doc(tenantId).update({
                status: 'key_provisioning_failed',
                error: error instanceof Error ? error.message : String(error),
                updated_at: new Date(),
            });
        }
        catch (updateError) {
            firebase_functions_1.logger.error(`Failed to update tenant status after error: ${tenantId}`, {
                tenantId,
                updateError: updateError instanceof Error ? updateError.message : String(updateError),
            });
        }
        throw error;
    }
});
/**
 * Create API key for the tenant using Google API Keys service in MCP project
 */
async function createApiKey(tenantId, apiKeysClient) {
    const keyName = `mcp-rules-api-${tenantId}`;
    const displayName = `MCP Rules Engine API Key - ${tenantId}`;
    const [operation] = await apiKeysClient.createKey({
        parent: `projects/${MCP_PROJECT}/locations/global`,
        keyId: keyName,
        key: {
            displayName,
            restrictions: {
            // Add API restrictions if needed
            // For now, we'll create keys without specific API restrictions
            },
        },
    });
    // Wait for the operation to complete
    const [key] = await operation.promise();
    if (!key.keyString) {
        throw new Error(`Failed to create API key for tenant: ${tenantId} in project: ${MCP_PROJECT}`);
    }
    firebase_functions_1.logger.info(`Created API key for tenant: ${tenantId} in project: ${MCP_PROJECT}`, {
        tenantId,
        keyName: key.name,
        project: MCP_PROJECT,
    });
    return key.keyString;
}
/**
 * Store API key in Secret Manager in MCP project
 */
async function storeApiKeyInSecretManager(tenantId, apiKey, secretManagerClient) {
    const secretId = `mcp-key-${tenantId}`;
    const parent = `projects/${MCP_PROJECT}`;
    // Create the secret
    const [secret] = await secretManagerClient.createSecret({
        parent,
        secretId,
        secret: {
            labels: {
                tenant: tenantId,
                service: 'mcp-rules-engine',
                environment: process.env.NODE_ENV || 'production',
                managed_by: 'cloud-function',
                tenant_project: TENANT_PROJECT,
            },
            replication: {
                automatic: {},
            },
        },
    });
    // Add the secret version with the API key
    await secretManagerClient.addSecretVersion({
        parent: secret.name,
        payload: {
            data: Buffer.from(apiKey, 'utf8'),
        },
    });
    firebase_functions_1.logger.info(`Stored API key in Secret Manager for tenant: ${tenantId} in project: ${MCP_PROJECT}`, {
        tenantId,
        secretName: secret.name,
        project: MCP_PROJECT,
    });
    return secret.name;
}
/**
 * Grant access to the secret for AiLex service accounts in MCP project
 */
async function grantSecretAccess(secretName, secretManagerClient) {
    const members = [
        `serviceAccount:${AILEX_SERVICE_ACCOUNT}`,
        `serviceAccount:${MCP_CLIENT_SERVICE_ACCOUNT}`,
    ];
    // Grant secretAccessor role to service accounts
    await secretManagerClient.setIamPolicy({
        resource: secretName,
        policy: {
            bindings: [
                {
                    role: 'roles/secretmanager.secretAccessor',
                    members,
                },
            ],
        },
    });
    firebase_functions_1.logger.info(`Granted secret access to service accounts in project: ${MCP_PROJECT}`, {
        secretName,
        members,
        project: MCP_PROJECT,
    });
}
/**
 * Update tenant document with secret path and active status
 */
async function updateTenantDocument(tenantId, secretName) {
    await db.collection('tenants').doc(tenantId).update({
        mcpSecretPath: secretName,
        status: 'active',
        updated_at: new Date(),
    });
    firebase_functions_1.logger.info(`Updated tenant document`, {
        tenantId,
        secretName,
        status: 'active',
    });
}
//# sourceMappingURL=onTenantCreate.js.map