/**
 * Jest configuration for tenant onboarding tests
 */

module.exports = {
  displayName: 'Tenant Onboarding E2E Tests',
  testMatch: ['**/tests/onboard.e2e.spec.ts'],
  preset: 'ts-jest',
  testEnvironment: 'node',
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/jest.setup.onboard.js'],
  
  // Module resolution
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@backend/(.*)$': '<rootDir>/backend/$1',
    '^@cloud-functions/(.*)$': '<rootDir>/cloud-functions/$1',
  },
  
  // Transform configuration
  transform: {
    '^.+\\.tsx?$': ['ts-jest', {
      tsconfig: {
        compilerOptions: {
          module: 'commonjs',
          target: 'es2017',
          esModuleInterop: true,
          allowSyntheticDefaultImports: true,
          skipLibCheck: true,
        },
      },
    }],
  },
  
  // Coverage configuration
  collectCoverageFrom: [
    'cloud-functions/**/*.ts',
    'backend/agents/interactive/deadline/tools/deadlinesTool.ts',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/lib/**',
  ],
  
  coverageReporters: ['text', 'lcov', 'html'],
  coverageDirectory: 'coverage/onboard',
  
  // Test timeout
  testTimeout: 30000,
  
  // Environment variables for testing
  testEnvironment: 'node',
  
  // Mock configuration
  clearMocks: true,
  restoreMocks: true,
  
  // Verbose output
  verbose: true,
  
  // Global setup/teardown
  globalSetup: '<rootDir>/tests/setup/global-setup.js',
  globalTeardown: '<rootDir>/tests/setup/global-teardown.js',
};
