"""
CopilotKit handler for the Deadline Agent.

This module provides the interface between CopilotKit and the Deadline Agent,
handling request processing, state management, and response formatting.
"""

import logging
from typing import Any, Dict, List, Optional
import uuid
from datetime import datetime

from backend.agents.interactive.deadline.deadlineAgent import (
    deadlineAgent,
    shouldTriggerDeadlineAgent,
    DeadlineAgentState,
)
from backend.agents.interactive.deadline.tools.deadlinesTool import DeadlinesOutput

# Set up logging
logger = logging.getLogger(__name__)


class DeadlineCopilotHandler:
    """
    Handler for CopilotKit integration with the Deadline Agent.
    
    This class manages the interaction between CopilotKit requests and the
    Deadline Agent, including state management and response formatting.
    """

    def __init__(self):
        """Initialize the deadline copilot handler."""
        self.agent_name = "deadline_agent"
        logger.info(f"Initialized {self.agent_name} CopilotKit handler")

    async def handle_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle a CopilotKit request for deadline calculation.

        Args:
            data: Request data from CopilotKit containing messages and context

        Returns:
            Dict containing the response in CopilotKit format
        """
        try:
            logger.info(f"Processing deadline request: {str(data)[:200]}...")

            # Extract request information
            messages = data.get("messages", [])
            thread_id = data.get("threadId", str(uuid.uuid4()))
            
            if not messages:
                return self._create_error_response(
                    "No messages provided", thread_id
                )

            # Get the latest user message
            user_message = None
            for msg in reversed(messages):
                if msg.get("role") == "user":
                    user_message = msg.get("content", "")
                    break

            if not user_message:
                return self._create_error_response(
                    "No user message found", thread_id
                )

            # Check if this should trigger the deadline agent
            if not shouldTriggerDeadlineAgent(user_message):
                return self._create_response(
                    "I can help you calculate legal deadlines. Please provide information about the jurisdiction, trigger event, and date.",
                    thread_id,
                    done=True
                )

            # Convert messages to agent format
            agent_messages = self._convert_messages_to_agent_format(messages)

            # Create agent state
            agent_state = DeadlineAgentState(
                messages=agent_messages
            )

            # Run the deadline agent
            logger.info("Invoking deadline agent...")
            result = await deadlineAgent(agent_state, {})

            # Process the result
            if result.get("error"):
                return self._create_error_response(
                    result["error"], thread_id
                )

            # Extract deadlines result
            deadlines_result = result.get("deadlinesResult")
            response_messages = result.get("messages", [])

            if deadlines_result:
                return self._create_deadlines_response(
                    deadlines_result, thread_id
                )
            elif response_messages:
                # Extract content from AI message
                ai_message = response_messages[-1]
                content = ai_message.content
                
                try:
                    import json
                    parsed_content = json.loads(content)
                    if parsed_content.get("role") == "deadline_results":
                        return self._create_deadlines_response(
                            parsed_content.get("data"), thread_id
                        )
                    else:
                        return self._create_response(
                            parsed_content.get("message", content), thread_id
                        )
                except (json.JSONDecodeError, AttributeError):
                    return self._create_response(content, thread_id)
            else:
                return self._create_error_response(
                    "No response from deadline agent", thread_id
                )

        except Exception as e:
            logger.exception(f"Error in deadline copilot handler: {str(e)}")
            return self._create_error_response(
                "An error occurred while processing your deadline request", 
                data.get("threadId", "error-thread")
            )

    def _convert_messages_to_agent_format(self, messages: List[Dict[str, Any]]) -> List[Any]:
        """Convert CopilotKit messages to LangChain message format."""
        from langchain_core.messages import HumanMessage, AIMessage
        
        agent_messages = []
        for msg in messages:
            role = msg.get("role", "user")
            content = msg.get("content", "")
            
            if role == "user":
                agent_messages.append(HumanMessage(content=content))
            elif role == "assistant":
                agent_messages.append(AIMessage(content=content))
        
        return agent_messages

    def _create_deadlines_response(
        self, 
        deadlines_result: Optional[DeadlinesOutput], 
        thread_id: str
    ) -> Dict[str, Any]:
        """Create a formatted response with deadline results."""
        if not deadlines_result or not deadlines_result.deadlines:
            return self._create_response(
                "No deadlines found for the specified criteria.", thread_id
            )

        # Format deadlines for display
        deadlines_text = self._format_deadlines_text(deadlines_result)
        
        return {
            "messages": [
                {
                    "content": [deadlines_text],
                    "role": "assistant",
                    "additional_kwargs": {
                        "role": "deadline_results",
                        "deadlines": [
                            {
                                "id": d.id,
                                "name": d.name,
                                "dueDate": d.dueDate,
                                "priority": d.priority,
                                "category": d.category,
                                "description": d.description,
                                "legalBasis": d.legalBasis,
                                "consequences": d.consequences,
                            }
                            for d in deadlines_result.deadlines
                        ],
                        "metadata": {
                            "jurisdiction": deadlines_result.jurisdiction,
                            "triggerCode": deadlines_result.triggerCode,
                            "startDate": deadlines_result.startDate,
                            "practiceArea": deadlines_result.practiceArea,
                            "calculatedAt": deadlines_result.calculatedAt,
                            "source": deadlines_result.source,
                        },
                    },
                }
            ],
            "done": True,
            "threadId": thread_id,
        }

    def _format_deadlines_text(self, deadlines_result: DeadlinesOutput) -> str:
        """Format deadlines result as readable text."""
        if not deadlines_result.deadlines:
            return "No deadlines found for the specified criteria."

        lines = [
            f"📅 **Legal Deadlines for {deadlines_result.jurisdiction}**",
            f"Trigger: {deadlines_result.triggerCode}",
            f"Start Date: {deadlines_result.startDate}",
            f"Practice Area: {deadlines_result.practiceArea}",
            "",
            f"Found {len(deadlines_result.deadlines)} deadline(s):",
            "",
        ]

        for i, deadline in enumerate(deadlines_result.deadlines, 1):
            priority_emoji = {
                "high": "🔴",
                "medium": "🟡", 
                "low": "🟢"
            }.get(deadline.priority, "⚪")
            
            lines.extend([
                f"{i}. {priority_emoji} **{deadline.name}**",
                f"   📅 Due: {deadline.dueDate}",
                f"   📋 Category: {deadline.category}",
            ])
            
            if deadline.description:
                lines.append(f"   📝 {deadline.description}")
            
            if deadline.legalBasis:
                lines.append(f"   ⚖️ Legal Basis: {deadline.legalBasis}")
            
            lines.append("")

        lines.extend([
            f"*Calculated at {deadlines_result.calculatedAt}*",
            f"*Source: {deadlines_result.source}*"
        ])

        return "\n".join(lines)

    def _create_response(
        self, 
        content: str, 
        thread_id: str, 
        done: bool = True
    ) -> Dict[str, Any]:
        """Create a standard CopilotKit response."""
        return {
            "messages": [
                {
                    "content": [content],
                    "role": "assistant",
                }
            ],
            "done": done,
            "threadId": thread_id,
        }

    def _create_error_response(self, error_message: str, thread_id: str) -> Dict[str, Any]:
        """Create an error response in CopilotKit format."""
        return {
            "messages": [
                {
                    "content": [f"❌ {error_message}"],
                    "role": "assistant",
                }
            ],
            "done": True,
            "threadId": thread_id,
        }
