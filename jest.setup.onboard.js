/**
 * Jest setup for tenant onboarding tests
 * Configures mocks for Google Cloud services and Firebase
 */

// Mock Google Cloud API Keys client
jest.mock('@google-cloud/apikeys', () => ({
  ApiKeysClient: jest.fn().mockImplementation(() => ({
    createKey: jest.fn(),
    deleteKey: jest.fn(),
    getKey: jest.fn(),
    listKeys: jest.fn(),
  })),
}));

// Mock Google Cloud Secret Manager client
jest.mock('@google-cloud/secret-manager', () => ({
  SecretManagerServiceClient: jest.fn().mockImplementation(() => ({
    createSecret: jest.fn(),
    deleteSecret: jest.fn(),
    addSecretVersion: jest.fn(),
    accessSecretVersion: jest.fn(),
    setIamPolicy: jest.fn(),
    getIamPolicy: jest.fn(),
  })),
}));

// Mock Firebase Admin
jest.mock('firebase-admin/app', () => ({
  initializeApp: jest.fn(),
}));

jest.mock('firebase-admin/firestore', () => ({
  getFirestore: jest.fn(() => ({
    collection: jest.fn(() => ({
      doc: jest.fn(() => ({
        get: jest.fn(),
        set: jest.fn(),
        update: jest.fn(),
        delete: jest.fn(),
      })),
    })),
  })),
}));

// Mock Firebase Functions
jest.mock('firebase-functions/v2/firestore', () => ({
  onDocumentCreated: jest.fn((config, handler) => handler),
}));

jest.mock('firebase-functions', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock environment variables
process.env.GOOGLE_CLOUD_PROJECT = 'texas-laws-personalinjury';
process.env.NODE_ENV = 'test';
process.env.MCP_RULES_BASE = 'https://rules.ailexlaw.com';
process.env.REDIS_URL = 'redis://localhost:6379';

// Global test utilities
global.testUtils = {
  generateTenantId: () => `test-tenant-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  generateApiKey: () => `test-api-key-${Date.now()}`,
  generateSecretName: (tenantId) => `projects/texas-laws-personalinjury/secrets/mcp-key-${tenantId}`,
  
  // Helper to create mock tenant data
  createMockTenant: (overrides = {}) => ({
    id: global.testUtils.generateTenantId(),
    name: 'Test Law Firm',
    status: 'pending_key',
    created_at: new Date(),
    ...overrides,
  }),
  
  // Helper to create mock Cloud Function event
  createMockEvent: (tenantId, tenantData) => ({
    params: { tenantId },
    data: {
      data: () => tenantData,
    },
  }),
};

// Setup test database connection mocks
beforeAll(() => {
  // Mock Supabase client if needed
  jest.mock('@supabase/supabase-js', () => ({
    createClient: jest.fn(() => ({
      from: jest.fn(() => ({
        select: jest.fn().mockReturnThis(),
        insert: jest.fn().mockReturnThis(),
        update: jest.fn().mockReturnThis(),
        delete: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn(),
      })),
      schema: jest.fn(() => ({
        from: jest.fn(() => ({
          select: jest.fn().mockReturnThis(),
          insert: jest.fn().mockReturnThis(),
          update: jest.fn().mockReturnThis(),
          delete: jest.fn().mockReturnThis(),
          eq: jest.fn().mockReturnThis(),
          single: jest.fn(),
        })),
      })),
    })),
  }));
  
  // Mock Redis client
  jest.mock('ioredis', () => {
    return jest.fn().mockImplementation(() => ({
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      exists: jest.fn(),
      expire: jest.fn(),
      disconnect: jest.fn(),
    }));
  });
});

// Cleanup after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Increase timeout for integration tests
jest.setTimeout(30000);
