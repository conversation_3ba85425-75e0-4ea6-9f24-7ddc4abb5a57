/**
 * Type definitions for database tables
 */

// Common types
export type UUID = string;
export type Timestamp = string;
export type Json = Record<string, unknown>;

// Tenant schema types
export interface Tenant {
  id: UUID;
  tenant_id: UUID;
  name: string;
  state_bar_number: string;
  firm_type: string;
  year_established?: number;
  tax_id?: string;
  primary_email: string;
  secondary_email?: string;
  phone: string;
  fax?: string;
  website_url?: string;
  address: Json;
  practice_areas?: string[];
  specializations?: string[];
  admin_user_id?: UUID;
  status: string;
  verification_status: string;
  subscription_tier: string;
  subscription_status: string;
  settings?: Json;
  metadata?: Json;
  created_at?: Timestamp;
  updated_at?: Timestamp;
  jurisdiction_settings?: Json;
  // MCP-related fields for automated key provisioning
  mcp_secret_path?: string;
  mcp_status?: 'pending_key' | 'active' | 'inactive' | 'key_provisioning_failed';
}

export interface TenantUser {
  id: UUID;
  email: string;
  role: string;
  tenant_id: UUID;
  created_at?: Timestamp;
  last_login?: Timestamp;
  settings?: Json;
  firm_role?: string;
  auth_user_id?: UUID;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  updated_at?: Timestamp;
}

export interface TenantQuota {
  id: UUID;
  tenant_id: UUID;
  max_daily_uploads: number;
  max_monthly_uploads: number;
  max_document_size_mb: number;
  max_concurrent_processing: number;
  plan_tier: string;
  updated_at?: Timestamp;
  subscription_id?: UUID;
  usage_type?: string;
  quota_limit?: number;
  reset_frequency?: string;
  created_at?: Timestamp;
}

export interface ResourceUsage {
  id: UUID;
  tenant_id: UUID;
  usage_type: string;
  usage_count: number;
  resource_size_bytes?: number;
  period_start: Timestamp;
  period_end: Timestamp;
  created_at?: Timestamp;
}

export interface SubscriptionPlan {
  id: UUID;
  name: string;
  code: string;
  description?: string;
  is_active?: boolean;
  is_public?: boolean;
  base_price_monthly: number;
  base_price_yearly: number;
  features: Json;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}

export interface SubscriptionAddon {
  id: UUID;
  name: string;
  code: string;
  description?: string;
  category: string;
  is_active?: boolean;
  price_monthly: number;
  price_yearly: number;
  features: Json;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}

export interface TenantSubscription {
  id: UUID;
  tenant_id: UUID;
  plan_id: UUID;
  status: string;
  billing_cycle: string;
  trial_start?: Timestamp;
  trial_end?: Timestamp;
  current_period_start: Timestamp;
  current_period_end: Timestamp;
  canceled_at?: Timestamp;
  payment_provider?: string;
  payment_provider_subscription_id?: string;
  metadata?: Json;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}

export interface TenantAddon {
  id: UUID;
  tenant_id: UUID;
  subscription_id: UUID;
  addon_id: UUID;
  status: string;
  quantity: number;
  current_period_start: Timestamp;
  current_period_end: Timestamp;
  canceled_at?: Timestamp;
  metadata?: Json;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}

// Security schema types
export interface SecurityEvent {
  id: UUID;
  user_id?: UUID;
  tenant_id?: UUID;
  event_type: string;
  severity: string;
  details: Json;
  ip_address?: string;
  user_agent?: string;
  created_at?: Timestamp;
}

export interface AlertConfig {
  id: UUID;
  user_id: UUID;
  email: boolean;
  in_app: boolean;
  sms: boolean;
  min_severity: 'low' | 'medium' | 'high' | 'critical';
  created_at?: Timestamp;
  updated_at?: Timestamp;
}

export interface DeviceFingerprint {
  id: UUID;
  user_id: UUID;
  fingerprint: string;
  device_name?: string;
  device_type?: string;
  browser?: string;
  os?: string;
  trusted: boolean;
  last_used?: Timestamp;
  created_at?: Timestamp;
}

// Public schema types
export interface Document {
  id: UUID;
  tenant_id: UUID;
  user_id: UUID;
  title: string;
  file_name: string;
  file_type: string;
  file_size: number;
  status: string;
  metadata?: Json;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}

export interface Case {
  id: UUID;
  tenant_id: UUID;
  title: string;
  description?: string;
  status: string;
  created_by: UUID;
  assigned_to?: UUID[];
  metadata?: Json;
  created_at?: Timestamp;
  updated_at?: Timestamp;
}

// Add more types as needed
