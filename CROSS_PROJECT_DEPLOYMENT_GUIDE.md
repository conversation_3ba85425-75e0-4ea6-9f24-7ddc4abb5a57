# Cross-Project Deployment Guide for Automated Tenant Onboarding

This guide implements the recommended cross-project architecture where:
- **Tenant data and Cloud Function** live in `new-texas-laws`
- **API Keys and Secrets** are created in `texas-laws-personalinjury` (where MCP Gateway runs)
- **Service account impersonation** enables secure cross-project access

## Architecture Overview

```
┌───────────────────────────────┐
│  Supabase (new-texas-laws)    │──┐  (ROW INSERT → Firestore)
└───────────────────────────────┘  │
                                   ▼
┌────────────────────────────────────────────────────────┐
│ Cloud Function "onTenantCreate"  (new-texas-laws)      │
│  1. Creates API key **in texas-laws-personalinjury**   │
│  2. Creates Secret **in texas-laws-personalinjury**    │
│  3. Updates Supabase row with secret path              │
└────────────────────────────────────────────────────────┘
                                   ▼
┌───────────────────────────────┐
│  AiLex backend (Cloud Run,   │
│  texas-laws-personalinjury)  │── uses secret → x-api-key
└───────────────────────────────┘
```

## Step-by-Step Deployment

### Step 1: Create Cross-Project Service Account

**In `texas-laws-personalinjury` project:**

1. **Create MCP Key Provisioner Service Account**:
   ```bash
   gcloud config set project texas-laws-personalinjury
   
   gcloud iam service-accounts create mcp-key-provisioner \
     --display-name="MCP Key Provisioner Service Account" \
     --description="Cross-project service account for creating API keys and secrets"
   ```

2. **Grant Required Roles**:
   ```bash
   # API Keys Admin
   gcloud projects add-iam-policy-binding texas-laws-personalinjury \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/apikeys.admin"
   
   # Secret Manager Admin
   gcloud projects add-iam-policy-binding texas-laws-personalinjury \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/secretmanager.admin"
   ```

### Step 2: Create Cloud Function Service Account

**In `new-texas-laws` project:**

1. **Create Tenant Onboard Function Service Account**:
   ```bash
   gcloud config set project new-texas-laws
   
   gcloud iam service-accounts create tenant-onboard-function \
     --display-name="Tenant Onboarding Cloud Function Service Account" \
     --description="Service account for automated tenant MCP key provisioning"
   ```

2. **Grant Firestore Access**:
   ```bash
   gcloud projects add-iam-policy-binding new-texas-laws \
     --member="serviceAccount:<EMAIL>" \
     --role="roles/datastore.user"
   ```

### Step 3: Enable Cross-Project Impersonation

**Grant impersonation permission:**

```bash
gcloud iam service-accounts add-iam-policy-binding \
  <EMAIL> \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/iam.serviceAccountTokenCreator" \
  --project=texas-laws-personalinjury
```

### Step 4: Enable Required APIs

**In both projects:**

```bash
# In new-texas-laws
gcloud config set project new-texas-laws
gcloud services enable cloudfunctions.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable firestore.googleapis.com
gcloud services enable iam.googleapis.com

# In texas-laws-personalinjury
gcloud config set project texas-laws-personalinjury
gcloud services enable apikeys.googleapis.com
gcloud services enable secretmanager.googleapis.com
gcloud services enable iam.googleapis.com
```

### Step 5: Deploy Cloud Function

**In `new-texas-laws` project:**

```bash
cd cloud-functions

# Install dependencies
npm install

# Deploy function with cross-project environment variables
gcloud functions deploy tenant-onboard \
  --gen2 \
  --runtime=nodejs20 \
  --region=us-central1 \
  --source=. \
  --entry-point=onTenantCreate \
  --trigger-event-filters="type=google.cloud.firestore.document.v1.created" \
  --trigger-event-filters="database=(default)" \
  --trigger-event-filters-path-pattern="document=tenants/{tenantId}" \
  --service-account=<EMAIL> \
  --set-env-vars="TENANT_PROJECT=new-texas-laws,MCP_PROJECT=texas-laws-personalinjury,MCP_KEY_PROVISIONER_SA=<EMAIL>,NODE_ENV=production" \
  --memory=256Mi \
  --timeout=30s \
  --project=new-texas-laws
```

### Step 6: Test Cross-Project Integration

1. **Create Test Tenant in Supabase**:
   ```sql
   INSERT INTO tenants.firms (
     tenant_id,
     name,
     state_bar_number,
     firm_type,
     primary_email,
     phone,
     address,
     subscription_status,
     mcp_status
   ) VALUES (
     gen_random_uuid(),
     'Cross-Project Test Firm',
     'TX888888',
     'Solo Practice',
     '<EMAIL>',
     '555-0199',
     '{"street": "123 Cross St", "city": "Austin", "state": "TX", "zip": "78701"}',
     'active',
     'pending_key'
   );
   ```

2. **Create Firestore Trigger Document**:
   ```javascript
   // In Firestore console (new-texas-laws project), create document in 'tenants' collection
   {
     "id": "cross-project-test-123",
     "name": "Cross-Project Test Firm",
     "status": "pending_key",
     "created_at": new Date()
   }
   ```

3. **Verify Results**:
   - **Check Cloud Function Logs** (new-texas-laws):
     ```bash
     gcloud functions logs read tenant-onboard --project=new-texas-laws --limit=20
     ```
   
   - **Check API Key Created** (texas-laws-personalinjury):
     ```bash
     gcloud alpha services api-keys list --project=texas-laws-personalinjury
     ```
   
   - **Check Secret Created** (texas-laws-personalinjury):
     ```bash
     gcloud secrets list --project=texas-laws-personalinjury
     ```
   
   - **Verify Supabase Update**:
     ```sql
     SELECT id, name, mcp_status, mcp_secret_path 
     FROM tenants.firms 
     WHERE name = 'Cross-Project Test Firm';
     ```

## Verification Checklist

- [ ] **Service Accounts Created**: Both projects have required service accounts
- [ ] **Cross-Project IAM**: Impersonation permission granted
- [ ] **APIs Enabled**: All required APIs enabled in both projects
- [ ] **Cloud Function Deployed**: Function running in new-texas-laws
- [ ] **Test Successful**: API key and secret created in texas-laws-personalinjury
- [ ] **Secret Path Correct**: Points to texas-laws-personalinjury project
- [ ] **Backend Access**: AiLex backend can read secrets

## Expected Secret Path Format

Secrets will be stored with paths like:
```
projects/texas-laws-personalinjury/secrets/mcp-key-{tenant-id}/versions/latest
```

This ensures:
- ✅ **API Keys** are visible in texas-laws-personalinjury Gateway logs
- ✅ **Secrets** are accessible by existing AiLex backend service accounts
- ✅ **Quotas** are tracked in the correct project
- ✅ **Cross-Project** integration works seamlessly

## Troubleshooting

### Common Issues

1. **Impersonation Permission Denied**:
   - Verify the IAM binding was created correctly
   - Check that both service accounts exist
   - Ensure the Cloud Function is using the correct service account

2. **API Key Creation Fails**:
   - Verify API Keys API is enabled in texas-laws-personalinjury
   - Check that mcp-key-provisioner has apikeys.admin role

3. **Secret Creation Fails**:
   - Verify Secret Manager API is enabled in texas-laws-personalinjury
   - Check that mcp-key-provisioner has secretmanager.admin role

4. **Function Not Triggering**:
   - Verify Firestore trigger configuration
   - Check that the document path matches: `tenants/{tenantId}`
   - Ensure Firestore API is enabled in new-texas-laws

### Debug Commands

```bash
# Check service account impersonation
gcloud auth print-access-token --impersonate-service-account=<EMAIL>

# Test API key creation manually
gcloud alpha services api-keys create --display-name="test-key" --project=texas-laws-personalinjury

# Test secret creation manually
echo "test-secret" | gcloud secrets create test-secret --data-file=- --project=texas-laws-personalinjury
```

## Success Criteria

✅ **Cross-Project Architecture**: Function in new-texas-laws creates resources in texas-laws-personalinjury  
✅ **Service Account Impersonation**: Secure cross-project access working  
✅ **API Key Visibility**: Keys appear in texas-laws-personalinjury Gateway logs  
✅ **Secret Access**: AiLex backend can read secrets without additional IAM  
✅ **Automated Flow**: New tenants automatically get provisioned keys  

The cross-project automated tenant onboarding system is now ready! 🚀
